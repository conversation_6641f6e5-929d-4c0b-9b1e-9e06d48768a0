package com.ruoyi.diagnosis.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.file.MinioFileUtils;
import com.ruoyi.datasync.domain.PacsPatientStudy;
import com.ruoyi.datasync.mapper.PacsPatientStudyMapper;
import com.ruoyi.diagnosis.domain.Diagnosis;

import com.ruoyi.diagnosis.mapper.DiagnosisMapper;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.diagnosis.config.DiagnosisPdfConfig;
import com.ruoyi.diagnosis.config.SeleniumConfig;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 诊断PDF生成服务
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "diagnosis.pdf.enabled", havingValue = "true", matchIfMissing = true)
public class DiagnosisPdfGeneratorService {

    @Resource
    private DiagnosisMapper diagnosisMapper;

    @Resource
    private PacsPatientStudyMapper pacsPatientStudyMapper;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private MinioFileUtils minioFileUtils;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private DiagnosisPdfConfig diagnosisPdfConfig;

    @Resource
    private SeleniumConfig seleniumConfig;

    private WebDriverPool webDriverPool;
    private final ExecutorService executorService;

    public DiagnosisPdfGeneratorService() {
        this.executorService = Executors.newFixedThreadPool(1);
    }

    /**
     * 初始化WebDriver池（仅在启用时）
     */
    private void initializeWebDriverPool() {
        if (diagnosisPdfConfig.isChromeDriverEnabled() && seleniumConfig.isEnabled()) {
            try {
                String chromeDriverPath = seleniumConfig.getCurrentChromeDriverPath();
                String chromePath = seleniumConfig.getCurrentChromePath();
                
                log.info("正在初始化WebDriver池，Chrome路径: {}, Driver路径: {}", chromePath, chromeDriverPath);
                this.webDriverPool = new WebDriverPool(1, chromeDriverPath, chromePath);
                log.info("WebDriver池初始化成功");
            } catch (Exception e) {
                log.warn("WebDriver池初始化失败，PDF生成功能将被禁用: {}", e.getMessage());
                this.webDriverPool = null;
            }
        } else {
            log.info("WebDriver功能已禁用，PDF生成将跳过");
            this.webDriverPool = null;
        }
    }

    @PreDestroy
    public void destroy() {
        executorService.shutdown();
        if (webDriverPool != null) {
            webDriverPool.shutdown();
        }
    }

    /**
     * 批量生成已审核但未生成PDF的诊断报告
     */
    public void generatePdfsForAuditedDiagnosis() {
        log.info("开始批量生成已审核诊断的PDF报告...");

        // 检查是否启用PDF生成功能
        if (!diagnosisPdfConfig.isEnabled()) {
            log.info("诊断PDF生成功能已关闭");
            return;
        }

        // 检查是否启用自动生成
        if (!diagnosisPdfConfig.getAutoGenerate().isEnabled()) {
            log.info("诊断PDF自动生成功能已关闭");
            return;
        }

        // 初始化WebDriver池（如果还未初始化）
        if (webDriverPool == null) {
            initializeWebDriverPool();
        }

        // 检查WebDriver是否可用
        if (webDriverPool == null) {
            log.warn("WebDriver不可用，跳过PDF生成");
            return;
        }

        // 查询已审核但未生成PDF的诊断记录
        List<Diagnosis> diagnosisList = diagnosisMapper.selectAuditedWithoutPdf();

        if (diagnosisList.isEmpty()) {
            log.info("没有需要生成PDF的诊断记录");
            return;
        }

        log.info("找到{}条需要生成PDF的诊断记录", diagnosisList.size());

        String reportGenerateUrl = getReportGenerateUrl();

        for (Diagnosis diagnosis : diagnosisList) {
            try {
                generatePdfForDiagnosis(diagnosis, reportGenerateUrl, true);
                log.info("成功生成诊断PDF，ID: {}", diagnosis.getId());
            } catch (Exception e) {
                log.error("生成诊断PDF失败，ID: {}, 错误: {}", diagnosis.getId(), e.getMessage(), e);
                // 更新失败状态
                updatePdfStatus(diagnosis.getId(), "failed", null, e.getMessage());
            }
        }

        log.info("批量生成PDF任务完成");
    }

    /**
     * 为单个诊断生成PDF报告
     */
    public void generatePdfForDiagnosis(Diagnosis diagnosis, String reportGenerateUrl, boolean updateStatus) {
        WebDriver driver = null;
        String diagnosisId = diagnosis.getId().toString();

        try {
            log.info("开始为诊断ID: {} 生成PDF报告", diagnosisId);

            // 检查WebDriver池是否可用
            if (webDriverPool == null) {
                initializeWebDriverPool();
                if (webDriverPool == null) {
                    log.warn("WebDriver不可用，无法生成PDF，诊断ID: {}", diagnosisId);
                    return;
                }
            }

            // 获取WebDriver
            driver = webDriverPool.borrowWebDriver(reportGenerateUrl);
            JavascriptExecutor js = (JavascriptExecutor) driver;

            // 检查页面状态
            String readyState = (String) js.executeScript("return document.readyState");
            log.info("页面状态: {}", readyState);

            // 获取当前页面的基础URL
            String baseUrl = (String) js.executeScript("return window.location.href.substring(0, window.location.href.lastIndexOf('/') + 1);");
            log.info("基础URL: {}", baseUrl);

            // 检查并加载必要的脚本
            loadRequiredScripts(js, baseUrl);

            // 注册字体
            registerFonts(js, baseUrl);

            // 准备报告数据
            Map<String, Object> reportData = prepareReportData(diagnosis);
            String reportDataJson = objectMapper.writeValueAsString(reportData);

            // 读取报告模板文件
            String templateContent = loadReportTemplate();
            if (StringUtils.isEmpty(templateContent)) {
                throw new RuntimeException("未找到报告模板文件");
            }

            log.info("报告数据和模板准备完成，开始生成PDF...");

            // 执行PDF生成
            String pdfBase64 = generatePdfWithScript(js, templateContent, reportDataJson);

            // 处理PDF结果
            if (updateStatus) {
                handlePdfResult(pdfBase64, diagnosisId);
            }

        } catch (Exception e) {
            log.error("为诊断ID: {} 生成PDF报告时出错", diagnosisId, e);
            if (updateStatus) {
                updatePdfStatus(diagnosis.getId(), "failed", null, e.getMessage());
            }
            throw new RuntimeException("生成PDF报告时发生错误: " + e.getMessage(), e);
        } finally {
            if (driver != null && webDriverPool != null) {
                webDriverPool.returnWebDriver(driver);
            }
        }
    }

    /**
     * 为指定诊断ID生成PDF
     */
    public void generatePdfByDiagnosisId(Long diagnosisId) throws Exception {
        // 检查PDF生成功能是否启用
        if (!diagnosisPdfConfig.isEnabled()) {
            throw new Exception("PDF生成功能已禁用");
        }

        Diagnosis diagnosis = diagnosisMapper.selectDiagnosisById(diagnosisId);

        if (diagnosis == null) {
            throw new Exception("未找到诊断记录，ID: " + diagnosisId);
        }

        if (!"2".equals(diagnosis.getStatus())) {
            throw new Exception("只能为已审核的诊断生成PDF报告");
        }

        // 检查WebDriver是否可用
        if (webDriverPool == null) {
            initializeWebDriverPool();
            if (webDriverPool == null) {
                throw new Exception("WebDriver不可用，无法生成PDF报告");
            }
        }

        String reportGenerateUrl = getReportGenerateUrl();
        if (StringUtils.isBlank(reportGenerateUrl)) {
            throw new Exception("生成PDF报告失败，没有配置报告生成地址");
        }

        generatePdfForDiagnosis(diagnosis, reportGenerateUrl, true);
    }

    /**
     * 检查并加载必要的脚本
     */
    private void loadRequiredScripts(JavascriptExecutor js, String baseUrl) throws InterruptedException {
        log.info("检查脚本是否已加载...");
        Boolean hasCore = false;
        Boolean hasPdf = false;

        // 尝试多次检查脚本加载状态
        for (int i = 0; i < 10; i++) {
            try {
                hasCore = (Boolean) js.executeScript("return typeof MESCIUS !== 'undefined' && " + "typeof MESCIUS.ActiveReportsJS !== 'undefined' && " + "typeof MESCIUS.ActiveReportsJS.Core !== 'undefined'");

                hasPdf = (Boolean) js.executeScript("return typeof MESCIUS !== 'undefined' && " + "typeof MESCIUS.ActiveReportsJS !== 'undefined' && " + "typeof MESCIUS.ActiveReportsJS.PdfExport !== 'undefined'");

                if (hasCore && hasPdf) {
                    log.info("脚本已加载");
                    return;
                }

                log.info("脚本未完全加载，等待中... (Core: {}, PDF: {})", hasCore, hasPdf);
                Thread.sleep(1000);
            } catch (Exception e) {
                log.warn("检查脚本加载状态时出错: {}", e.getMessage());
                Thread.sleep(1000);
            }
        }

        // 如果脚本未加载，尝试手动加载
        if (!hasCore || !hasPdf) {
            log.info("尝试手动加载脚本...");
            loadScriptsManually(js, baseUrl);
        }
    }

    /**
     * 手动加载脚本
     */
    private void loadScriptsManually(JavascriptExecutor js, String baseUrl) throws InterruptedException {
        // 清除现有脚本
        js.executeScript("var scripts = document.querySelectorAll('script[src*=\"ar-js\"]');" + "scripts.forEach(function(script) { script.parentNode.removeChild(script); });");

        // 加载核心脚本
        String coreScriptUrl = baseUrl + "scripts/ar-js-core.js";
        log.info("加载核心脚本: {}", coreScriptUrl);
        js.executeScript("var coreScript = document.createElement('script');" + "coreScript.src = arguments[0];" + "document.head.appendChild(coreScript);", coreScriptUrl);

        // 等待核心脚本加载
        waitForScriptLoad(js, "核心脚本", "return typeof MESCIUS !== 'undefined' && " + "typeof MESCIUS.ActiveReportsJS !== 'undefined' && " + "typeof MESCIUS.ActiveReportsJS.Core !== 'undefined'");

        // 加载PDF脚本
        String pdfScriptUrl = baseUrl + "scripts/ar-js-pdf.js";
        log.info("加载PDF脚本: {}", pdfScriptUrl);
        js.executeScript("var pdfScript = document.createElement('script');" + "pdfScript.src = arguments[0];" + "document.head.appendChild(pdfScript);", pdfScriptUrl);

        // 等待PDF脚本加载
        waitForScriptLoad(js, "PDF脚本", "return typeof MESCIUS !== 'undefined' && " + "typeof MESCIUS.ActiveReportsJS !== 'undefined' && " + "typeof MESCIUS.ActiveReportsJS.PdfExport !== 'undefined'");
    }

    /**
     * 等待脚本加载完成
     */
    private void waitForScriptLoad(JavascriptExecutor js, String scriptName, String checkScript) throws InterruptedException {
        log.info("等待{}加载...", scriptName);
        long startTime = System.currentTimeMillis();
        while (true) {
            try {
                Boolean loaded = (Boolean) js.executeScript(checkScript);
                if (loaded) {
                    log.info("{}加载成功", scriptName);
                    break;
                }

                if (System.currentTimeMillis() - startTime > 20000) {
                    log.error("{}加载超时", scriptName);
                    throw new RuntimeException(scriptName + "加载超时");
                }

                Thread.sleep(1000);
            } catch (Exception e) {
                if (System.currentTimeMillis() - startTime > 20000) {
                    log.error("{}加载超时: {}", scriptName, e.getMessage());
                    throw new RuntimeException(scriptName + "加载超时: " + e.getMessage());
                }
                Thread.sleep(1000);
            }
        }
    }

    /**
     * 注册字体
     */
    private void registerFonts(JavascriptExecutor js, String baseUrl) {
        log.info("注册字体...");
        try {
            // 从配置中获取字体配置路径
            String configFontPath = sysConfigService.selectConfigByKey("diagnosis.pdf.fontConfigPath");
            String fontConfigPath = StringUtils.isNotEmpty(configFontPath) ? configFontPath : "fontsConfig.json";
            String fontConfigUrl = baseUrl + fontConfigPath;

            log.info("字体配置URL: {}", fontConfigUrl);
            js.executeScript("if (typeof MESCIUS !== 'undefined' && " + "    typeof MESCIUS.ActiveReportsJS !== 'undefined' && " + "    typeof MESCIUS.ActiveReportsJS.Core !== 'undefined' && " + "    typeof MESCIUS.ActiveReportsJS.Core.FontStore !== 'undefined') {" + "  MESCIUS.ActiveReportsJS.Core.FontStore.registerFonts(arguments[0]);" + "}", fontConfigUrl);

            // 等待字体注册
            Thread.sleep(2000);
        } catch (Exception e) {
            log.warn("字体注册可能失败: {}", e.getMessage());
        }
    }

    /**
     * 使用JavaScript生成PDF
     */
    private String generatePdfWithScript(JavascriptExecutor js, String templateContent, String reportDataJson) {
        log.info("执行PDF生成...");

        // 从配置中获取作者信息
        String author = sysConfigService.selectConfigByKey("diagnosis.pdf.author");
        if (StringUtils.isEmpty(author)) {
            author = "PACS System";
        }

        String script = "var callback = arguments[arguments.length - 1];" + "try {" + "  console.log('开始生成PDF...');" + "  var templateStr = arguments[0];" + "  var reportData = arguments[1];" + "  var author = arguments[2];" + "  console.log('模板和报告数据接收成功');" + "  " + "  // 调用页面中的generateReport函数" + "  if (typeof window.generateReport === 'function') {" + "    console.log('调用generateReport函数...');" + "    window.generateReport({ templateStr: templateStr, reportData: reportData, author: author })" + "      .then(function(base64Data) {" + "        console.log('PDF生成成功，数据长度: ' + base64Data.length);" + "        callback(base64Data);" + "      })" + "      .catch(function(error) {" + "        console.error('PDF生成失败', error);" + "        callback('ERROR: ' + (error.message || error));" + "      });" + "  } else {" + "    console.error('generateReport函数不存在');" + "    callback('ERROR: generateReport function not found');" + "  }" + "} catch (error) {" + "  console.error('执行脚本异常', error);" + "  callback('ERROR: ' + (error.message || error));" + "}";

        // 执行脚本
        log.info("执行PDF生成脚本...");
        Object response = js.executeAsyncScript(script, templateContent, reportDataJson, author);

        // 处理响应
        if (response instanceof String result) {
            if (result.startsWith("ERROR: ")) {
                log.error("生成报告失败: {}", StringUtils.substring(result, 0, 500));
                throw new RuntimeException("报告生成失败: " + result);
            } else {
                log.info("PDF生成成功，处理结果...");
                return result;
            }
        } else {
            log.error("意外的JavaScript响应: {}", response);
            throw new RuntimeException("意外的JavaScript响应");
        }
    }

    /**
     * 准备报告数据（公开方法）
     */
    public Map<String, Object> prepareReportDataPublic(Diagnosis diagnosis) {
        return prepareReportData(diagnosis);
    }

    /**
     * 准备报告数据
     */
    private Map<String, Object> prepareReportData(Diagnosis diagnosis) {
        Map<String, Object> reportData = new HashMap<>();

        // 获取患者检查信息
        PacsPatientStudy study = null;

        // 安全地处理studyId转换
        if (StringUtils.isNotEmpty(diagnosis.getStudyId())) {
            try {
                study = pacsPatientStudyMapper.selectPacsPatientStudyById(Long.valueOf(diagnosis.getStudyId()));
            } catch (NumberFormatException e) {
                log.warn("studyId格式错误，无法转换为Long: {}", diagnosis.getStudyId());
            }
        }

        if (study == null) {
            if (StringUtils.isNotEmpty(diagnosis.getPatientId())) {
                study = pacsPatientStudyMapper.selectPacsPatientStudyByPatientId(diagnosis.getPatientId());
            }
        }
        if (study == null) {
            log.error("未找到对应的检查信息，无法生成报告");
            throw new RuntimeException("未找到对应的检查信息，无法生成报告");
        }

        // 基本信息 - 从SysConfig表读取
        reportData.put("hospitalName", sysConfigService.selectConfigByKey("diagnosis.pdf.hospitalName"));
        reportData.put("reportTitle", study.getModality() + sysConfigService.selectConfigByKey("diagnosis.pdf.reportTitle"));
        reportData.put("diagnosis", diagnosis);

        String qrCodeUrl = sysConfigService.selectConfigByKey("diagnosis.qrcode.url");
        String qrCode = StringUtils.replace(qrCodeUrl, "{examCode}", study.getOriginalPatientId());
        //String qrCode = qrCodeUrl + study.getOriginalPatientId();

        reportData.put("qrCode", qrCode);

        // 患者信息
        if (study.getAge() == null) {
            Date birthday = study.getPatientBirthday();
            if (birthday != null) {
                int age = DateUtils.differentDaysByMillisecond(birthday, new Date()) / 365;
                study.setAge(age);
            }
        }
        //对性别进行处理
        if (study.getPatientSex() != null) {
            if (study.getPatientSex().equals("M")||study.getPatientSex().equals("Male")) {
                study.setPatientSex("男");
            } else if (study.getPatientSex().equals("F")||study.getPatientSex().equals("Female")) {
                study.setPatientSex("女");
            }
        }

        reportData.put("study", study);

        // 获取报告医生信息
        Map<String, Object> reportDoctorInfo = getDoctorInfo(diagnosis.getDoctor());
        reportData.put("reportDoctor", reportDoctorInfo);

        // 获取审核医生信息
        Map<String, Object> auditDoctorInfo = getDoctorInfo(diagnosis.getAuditBy());
        reportData.put("auditDoctor", auditDoctorInfo);

        // 保持向后兼容性 - 获取当前登录用户的签名图片
        String doctorSignature = getCurrentUserSignature();
        reportData.put("doctorSignature", doctorSignature);

        return reportData;
    }

    /**
     * 获取医生信息（姓名和签名图片）
     */
    private Map<String, Object> getDoctorInfo(String doctorName) {
        Map<String, Object> doctorInfo = new HashMap<>();

        if (StringUtils.isEmpty(doctorName)) {
            doctorInfo.put("name", "");
            doctorInfo.put("signatureUrl", "");
            return doctorInfo;
        }

        try {
            // 根据用户名查询用户信息
            SysUser user = sysUserService.selectUserByUserName(doctorName);
            if (user != null) {
                // 设置医生姓名（优先使用昵称，其次用户名）
                String displayName = StringUtils.isNotEmpty(user.getNickName()) ? user.getNickName() : user.getUserName();
                doctorInfo.put("name", displayName);

                // 获取签名图片URL
                String signatureUrl = getUserSignatureUrl(user);
                doctorInfo.put("signatureUrl", signatureUrl);

                log.info("获取到医生信息: 姓名={}, 签名URL={}", displayName, signatureUrl);
            } else {
                // 如果找不到用户，只设置姓名
                doctorInfo.put("name", doctorName);
                doctorInfo.put("signatureUrl", getDefaultSignature());
                log.warn("未找到医生用户信息: {}, 使用默认签名", doctorName);
            }
        } catch (Exception e) {
            log.error("获取医生信息失败: {}", e.getMessage(), e);
            doctorInfo.put("name", doctorName);
            doctorInfo.put("signatureUrl", getDefaultSignature());
        }

        return doctorInfo;
    }

    /**
     * 获取用户签名图片URL
     */
    private String getUserSignatureUrl(SysUser user) {
        if (user == null) {
            return getDefaultSignature();
        }

        // 获取用户头像作为签名图片
        String avatar = user.getAvatar();
        if (StringUtils.isNotEmpty(avatar)) {
            // 如果是相对路径，转换为完整URL
            if (!avatar.startsWith("http")) {
                // 从配置中获取文件服务器地址
                String fileServerUrl = sysConfigService.selectConfigByKey("file.server.url");
                if (StringUtils.isEmpty(fileServerUrl)) {
                    // 默认使用MinIO地址
                    fileServerUrl = "http://localhost:9000";
                }
                avatar = fileServerUrl + "/" + avatar;
            }
            return avatar;
        } else {
            return getDefaultSignature();
        }
    }

    /**
     * 获取当前用户的签名图片
     */
    private String getCurrentUserSignature() {
        try {
            // 获取当前登录用户ID
            Long userId = SecurityUtils.getUserId();
            if (userId == null) {
                log.warn("未获取到当前登录用户ID，使用默认签名");
                return getDefaultSignature();
            }

            // 查询用户信息
            SysUser user = sysUserService.selectUserById(userId);
            if (user == null) {
                log.warn("未找到用户信息，用户ID: {}", userId);
                return getDefaultSignature();
            }

            return getUserSignatureUrl(user);
        } catch (Exception e) {
            log.error("获取用户签名图片失败: {}", e.getMessage(), e);
            return getDefaultSignature();
        }
    }

    /**
     * 获取默认签名
     */
    private String getDefaultSignature() {
        // 可以返回默认的签名图片URL或者空字符串
        String defaultSignature = sysConfigService.selectConfigByKey("diagnosis.pdf.defaultSignature");
        return StringUtils.isNotEmpty(defaultSignature) ? defaultSignature : "";
    }

    /**
     * 处理PDF结果
     */
    private void handlePdfResult(String pdfBase64, String diagnosisId) {
        try {
            // 将Base64转换为字节数组
            byte[] pdfBytes = Base64.getDecoder().decode(pdfBase64);

            // 构建文件名
            String fileName = "diagnosis_report_" + diagnosisId + "_" + System.currentTimeMillis() + ".pdf";

            // 直接上传字节数组到MinIO
            String minioUrl = minioFileUtils.uploadPdfToMinio(pdfBytes, fileName);

            if (StringUtils.isNotEmpty(minioUrl)) {
                // 更新诊断记录
                updatePdfStatusSafely(diagnosisId, "completed", minioUrl, null);
                log.info("PDF报告已上传至MinIO: {}, 文件大小: {} bytes", minioUrl, pdfBytes.length);
            } else {
                updatePdfStatusSafely(diagnosisId, "failed", null, "MinIO上传失败");
                log.error("PDF上传MinIO失败");
            }

        } catch (Exception e) {
            log.error("处理PDF结果失败: {}", e.getMessage(), e);
            updatePdfStatusSafely(diagnosisId, "failed", null, e.getMessage());
        }
    }

    /**
     * 安全地更新PDF状态（处理字符串ID）
     */
    private void updatePdfStatusSafely(String diagnosisId, String status, String pdfPath, String errorMsg) {
        if (StringUtils.isEmpty(diagnosisId)) {
            log.error("诊断ID为空，无法更新PDF状态");
            return;
        }

        try {
            Long id = Long.parseLong(diagnosisId);
            updatePdfStatus(id, status, pdfPath, errorMsg);
        } catch (NumberFormatException e) {
            log.error("诊断ID格式错误，无法转换为Long: {}", diagnosisId);
        }
    }

    /**
     * 更新PDF状态
     */
    private void updatePdfStatus(Long diagnosisId, String status, String pdfPath, String errorMsg) {
        try {
            Diagnosis diagnosis = new Diagnosis();
            diagnosis.setId(diagnosisId);
            diagnosis.setPdfStatus(status);
            diagnosis.setPdfReportPath(pdfPath);
            diagnosis.setPdfGenerateTime(new Date());

            diagnosisMapper.updateDiagnosis(diagnosis);

            if ("failed".equals(status) && StringUtils.isNotEmpty(errorMsg)) {
                log.error("诊断ID: {} PDF生成失败: {}", diagnosisId, errorMsg);
            }
        } catch (Exception e) {
            log.error("更新PDF状态失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取报告生成URL
     */
    private String getReportGenerateUrl() {
        // 从SysConfig表读取报告生成URL
        return sysConfigService.selectConfigByKey("diagnosis.pdf.reportGenerateUrl");
    }

    /**
     * 加载报告模板文件
     */
    private String loadReportTemplate() {
        try {
            log.info("开始加载报告模板文件...");

            // 从配置中获取模板路径
            String configTemplatePath = sysConfigService.selectConfigByKey("diagnosis.pdf.templatePath");
            String templatePath = StringUtils.isNotEmpty(configTemplatePath) ? configTemplatePath : "static/diagnosis-templates/report.rdlx-json";

            // 判断是否为网络URL
            if (isHttpUrl(templatePath)) {
                // 从网络URL下载模板文件
                return downloadTemplateFromUrl(templatePath);
            } else {
                // 从本地加载模板文件
                return loadTemplateFromLocal(templatePath);
            }

        } catch (Exception e) {
            log.error("加载报告模板失败: {}", e.getMessage(), e);
            throw new RuntimeException("加载报告模板失败: " + e.getMessage(), e);
        }
    }

    /**
     * 判断是否为HTTP URL
     */
    private boolean isHttpUrl(String path) {
        return StringUtils.isNotEmpty(path) && (path.toLowerCase().startsWith("http://") || path.toLowerCase().startsWith("https://"));
    }

    /**
     * 从网络URL下载模板文件
     */
    private String downloadTemplateFromUrl(String templateUrl) {
        try {
            log.info("从网络URL下载模板文件: {}", templateUrl);

            URL url = new URL(templateUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // 设置请求属性
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000); // 10秒连接超时
            connection.setReadTimeout(30000);    // 30秒读取超时
            connection.setRequestProperty("User-Agent", "PACS-PDF-Generator/1.0");

            // 检查响应码
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw new RuntimeException("HTTP请求失败，响应码: " + responseCode);
            }

            // 读取响应内容
            try (InputStream inputStream = connection.getInputStream()) {
                byte[] bytes = inputStream.readAllBytes();
                String content = new String(bytes, StandardCharsets.UTF_8);

                log.info("从网络URL成功下载模板文件，内容长度: {}", content.length());
                return content;
            }

        } catch (Exception e) {
            log.error("从网络URL下载模板文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("从网络URL下载模板文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从本地加载模板文件
     */
    private String loadTemplateFromLocal(String templatePath) {
        try {
            log.info("从本地加载模板文件: {}", templatePath);

            // 首先尝试从类路径加载
            try {
                var resource = getClass().getClassLoader().getResource(templatePath);
                if (resource != null) {
                    String content = new String(Files.readAllBytes(Paths.get(resource.toURI())));
                    log.info("从类路径成功加载模板文件: {}, 内容长度: {}", templatePath, content.length());
                    return content;
                }
            } catch (Exception e) {
                log.warn("从类路径加载模板失败: {}", e.getMessage());
            }

            // 尝试从文件系统加载（开发环境）
            try {
                String fullPath = "ruoyi-admin/src/main/resources/" + templatePath;
                if (Files.exists(Paths.get(fullPath))) {
                    String content = new String(Files.readAllBytes(Paths.get(fullPath)));
                    log.info("从文件系统成功加载模板文件: {}, 内容长度: {}", fullPath, content.length());
                    return content;
                }
            } catch (Exception e) {
                log.warn("从文件系统加载模板失败: {}", e.getMessage());
            }

            throw new RuntimeException("无法找到本地模板文件: " + templatePath);

        } catch (Exception e) {
            log.error("从本地加载模板文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("从本地加载模板文件失败: " + e.getMessage(), e);
        }
    }


}
