import Layout from '@/layout'

// 诊断管理路由
export default [
  {
    path: '/diagnosis',
    component: Layout,
    alwaysShow: true,
    name: 'Diagnosis',
    meta: { title: '诊断管理', icon: 'form' },

    children: [
      {
        path: 'workspace',
        component: () => import('@/views/diagnosis/DiagnosisWorkspace'),
        name: 'DiagnosisWorkspace',
        meta: { title: '诊断工作台', icon: 'monitor' }
      },
      {
        path: 'test-workspace',
        component: () => import('@/views/diagnosis/test-workspace'),
        name: 'TestWorkspace',
        meta: { title: '工作台测试', icon: 'cpu' },
        hidden: true
      },
      {
        path: 'simple-test',
        component: () => import('@/views/diagnosis/simple-test'),
        name: 'SimpleTest',
        meta: { title: '简单测试', icon: 'cpu' },
        hidden: true
      },
      {
        path: 'study-list',
        component: () => import('@/views/diagnosis/study-list'),
        name: 'StudyList',
        meta: { title: '检查列表(旧版)', icon: 'table' }
      },
      {
        path: 'editor',
        component: () => import('@/views/diagnosis/diagnose-editor-new'),
        name: 'DiagnosisEditor',
        meta: { title: '编写诊断', activeMenu: '/diagnosis/study-list' },
        hidden: true
      },
      {
        path: 'view',
        component: () => import('@/views/diagnosis/diagnose-editor-new'),
        name: 'DiagnosisView',
        meta: { title: '查看诊断', activeMenu: '/diagnosis/study-list' },
        hidden: true
      },
      {
        path: 'audit',
        component: () => import('@/views/diagnosis/audit/index'),
        name: 'DiagnosisAudit',
        meta: { title: '诊断审核', icon: 'validCode' },

      },
      {
        path: 'audit/view',
        component: () => import('@/views/diagnosis/audit/view'),
        name: 'DiagnosisAuditView',
        meta: { title: '审核详情', activeMenu: '/diagnosis/audit' },
        hidden: true
      },
      {
        path: 'diagnose-editor-new',
        component: () => import('@/views/diagnosis/diagnose-editor-new'),
        name: 'DiagnoseEditorNew',
        meta: { title: '诊断编辑器', activeMenu: '/diagnosis/study-list' },
        hidden: true
      },
      {
        path: 'template',
        component: () => import('@/views/diagnosis/template/index'),
        name: 'DiagnosisTemplate',
        meta: { title: '诊断文本模版', icon: 'documentation' }
      },
      {
        path: 'reportTemplate',
        component: () => import('@/views/diagnosis/reportTemplate/index'),
        name: 'ReportTemplate',
        meta: { title: '报告模版管理', icon: 'edit' }
      }
    ]
  }
]
