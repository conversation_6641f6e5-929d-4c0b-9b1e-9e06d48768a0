/**
 * 统一按钮样式 - 合并所有按钮相关样式
 * 包含：传统按钮样式、Element Plus按钮修复、医疗主题按钮
 */

@import './variables.module.scss';

/* ==================== Element Plus 按钮基础修复 ==================== */

.el-button {
  /* 使用Element Plus内置变量 */
  font-weight: var(--el-button-font-weight, var(--el-font-weight-primary));
  border-radius: var(--el-border-radius-base);
  transition: all var(--el-transition-duration);

  /* 默认按钮样式修复 */
  &:not(.is-link):not(.is-text):not(.is-plain) {
    /* 默认按钮 - 使用Element Plus变量系统 */
    &:not(.el-button--primary):not(.el-button--success):not(.el-button--warning):not(.el-button--danger):not(.el-button--info) {
      color: var(--el-button-text-color, var(--el-text-color-regular));
      background-color: var(--el-button-bg-color, var(--el-fill-color-blank));
      border-color: var(--el-button-border-color, var(--el-border-color));
      
      &:hover, &:focus {
        color: var(--el-button-hover-text-color, var(--el-color-primary));
        background-color: var(--el-button-hover-bg-color, var(--el-color-primary-light-9));
        border-color: var(--el-button-hover-border-color, var(--el-color-primary-light-7));
      }
      
      &:active {
        color: var(--el-button-active-text-color, var(--el-color-primary));
        background-color: var(--el-button-active-bg-color, var(--el-color-primary-light-9));
        border-color: var(--el-button-active-border-color, var(--el-color-primary));
      }
    }
    
    /* 有类型的实心按钮确保白色文字 */
    &.el-button--primary,
    &.el-button--success,
    &.el-button--warning,
    &.el-button--danger {
      color: var(--el-color-white);
      
      &:hover, &:focus, &:active, &.is-active {
        color: var(--el-color-white);
      }
    }
    
    /* info按钮保持Element Plus默认行为 */
    &.el-button--info {
      /* 让Element Plus自动处理info按钮的颜色 */
      /* 确保在不同背景下都有合适的对比度 */
    }
  }

  /* 朴素按钮样式 */
  &.is-plain {
    background-color: var(--el-fill-color-blank);
    
    &:hover, &:focus {
      background-color: var(--el-color-primary);
      border-color: var(--el-color-primary);
      color: var(--el-color-white);
    }
  }

  /* 文字按钮样式 */
  &.is-text {
    background-color: transparent;
    border-color: transparent;
    
    &:hover, &:focus {
      background-color: var(--el-color-primary-light-9);
    }
  }

  /* 链接按钮样式 */
  &.is-link {
    background-color: transparent;
    border-color: transparent;
    
    &:hover, &:focus {
      color: var(--el-color-primary-light-3);
    }
  }

  /* 禁用状态 */
  &.is-disabled,
  &.is-disabled:hover,
  &.is-disabled:focus {
    color: var(--el-button-disabled-text-color, var(--el-disabled-text-color));
    background-color: var(--el-button-disabled-bg-color, var(--el-disabled-bg-color));
    border-color: var(--el-button-disabled-border-color, var(--el-disabled-border-color));
  }

  /* 加载状态 */
  &.is-loading {
    position: relative;
    pointer-events: none;
    
    &:before {
      pointer-events: none;
      content: '';
      position: absolute;
      left: -1px;
      top: -1px;
      right: -1px;
      bottom: -1px;
      border-radius: inherit;
      background-color: var(--el-mask-color, rgba(255, 255, 255, 0.9));
    }
  }
}

/* ==================== 医疗主题按钮混合器 ==================== */

@mixin medicalBtn($color, $lightColor: null) {
  $light: if($lightColor, $lightColor, lighten($color, 10%));
  background: linear-gradient(135deg, $color 0%, $light 100%);
  border: none;
  box-shadow: 0 2px 8px rgba($color, 0.3);
  transition: all 0.3s ease;
  border-radius: 6px;
  font-weight: 500;
  color: var(--el-color-white);

  &:hover {
    background: linear-gradient(135deg, darken($color, 10%) 0%, $color 100%);
    box-shadow: 0 4px 12px rgba($color, 0.4);
    transform: translateY(-1px);
    color: var(--el-color-white);

    &:before,
    &:after {
      background: $color;
    }
  }

  &:focus, &:active {
    color: var(--el-color-white);
  }
}

@mixin colorBtn($color) {
  background: $color;

  &:hover {
    color: $color;

    &:before,
    &:after {
      background: $color;
    }
  }
}

/* ==================== 医疗主题按钮类 ==================== */

/* 医疗主题按钮样式 */
.medical-blue-btn {
  @include medicalBtn($medical-blue, $medical-blue-light);
}

.medical-green-btn {
  @include medicalBtn($medical-green);
}

.medical-orange-btn {
  @include medicalBtn($medical-orange);
}

.medical-red-btn {
  @include medicalBtn($medical-red);
}

/* 保持原有按钮样式兼容性 */
.blue-btn {
  @include medicalBtn($blue, $light-blue);
}

.light-blue-btn {
  @include medicalBtn($light-blue);
}

.red-btn {
  @include medicalBtn($red);
}

.pink-btn {
  @include medicalBtn($pink);
}

.green-btn {
  @include medicalBtn($green);
}

.tiffany-btn {
  @include medicalBtn($tiffany);
}

.yellow-btn {
  @include medicalBtn($yellow);
}

/* 医疗主题增强按钮 */
.medical-button {
  &.el-button--primary {
    background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-primary-light) 100%);
    border: none;
    box-shadow: 0 2px 8px rgba(0, 102, 204, 0.3);
    color: var(--el-color-white);

    &:hover, &:focus {
      background: linear-gradient(135deg, var(--medical-primary-dark) 0%, var(--medical-primary) 100%);
      box-shadow: 0 4px 12px rgba(0, 102, 204, 0.4);
      transform: translateY(-1px);
      color: var(--el-color-white);
    }

    &:active {
      transform: translateY(0);
      color: var(--el-color-white);
    }
  }

  &.el-button--success {
    background: linear-gradient(135deg, var(--medical-success) 0%, var(--el-color-success-light-3) 100%);
    border: none;
    box-shadow: 0 2px 8px rgba(46, 125, 50, 0.3);
    color: var(--el-color-white);

    &:hover, &:focus {
      background: linear-gradient(135deg, var(--el-color-success-dark-2) 0%, var(--medical-success) 100%);
      box-shadow: 0 4px 12px rgba(46, 125, 50, 0.4);
      transform: translateY(-1px);
      color: var(--el-color-white);
    }

    &:active {
      transform: translateY(0);
      color: var(--el-color-white);
    }
  }

  &.el-button--warning {
    background: linear-gradient(135deg, var(--medical-warning) 0%, var(--el-color-warning-light-3) 100%);
    border: none;
    box-shadow: 0 2px 8px rgba(250, 140, 22, 0.3);
    color: var(--el-color-white);

    &:hover, &:focus {
      background: linear-gradient(135deg, var(--el-color-warning-dark-2) 0%, var(--medical-warning) 100%);
      box-shadow: 0 4px 12px rgba(250, 140, 22, 0.4);
      transform: translateY(-1px);
      color: var(--el-color-white);
    }

    &:active {
      transform: translateY(0);
      color: var(--el-color-white);
    }
  }

  &.el-button--danger {
    background: linear-gradient(135deg, var(--medical-danger) 0%, var(--el-color-danger-light-3) 100%);
    border: none;
    box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
    color: var(--el-color-white);

    &:hover, &:focus {
      background: linear-gradient(135deg, var(--el-color-danger-dark-2) 0%, var(--medical-danger) 100%);
      box-shadow: 0 4px 12px rgba(255, 77, 79, 0.4);
      transform: translateY(-1px);
      color: var(--el-color-white);
    }

    &:active {
      transform: translateY(0);
      color: var(--el-color-white);
    }
  }
}

/* ==================== 传统按钮样式 ==================== */

.pan-btn {
  font-size: 14px;
  color: #fff;
  padding: 14px 36px;
  border-radius: 8px;
  border: none;
  outline: none;
  transition: 600ms ease all;
  position: relative;
  display: inline-block;

  &:hover {
    background: #fff;

    &:before,
    &:after {
      width: 100%;
      transition: 600ms ease all;
    }
  }

  &:before,
  &:after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 2px;
    width: 0;
    transition: 400ms ease all;
  }

  &::after {
    right: inherit;
    top: inherit;
    left: 0;
    bottom: 0;
  }
}

.custom-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  color: #fff;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  padding: 10px 15px;
  font-size: 14px;
  border-radius: 4px;
}
