<template>
  <el-dialog
    v-model="visible"
    :title="getTitle"
    width="90%"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    append-to-body
    class="report-dialog"
  >
    <template #header>
      <div class="dialog-header">
        <span>{{ getTitle }}</span>
        <div class="toolbar-buttons">
          <el-button
            type="success"
            :icon="DocumentCopy"
            @click="exportPDF()"
            :loading="btnLoading"
            size="small"
          >
            导出PDF
          </el-button>
          <el-button
            type="danger"
            :icon="Printer"
            @click="printReport()"
            :loading="btnLoading"
            size="small"
          >
            打印
          </el-button>
        </div>
      </div>
    </template>
    <div class="viewer-host">
      <Viewer ref="reportViewer" language="zh" />
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
  // 报表查看器包
  import { Core, PdfExport } from '@grapecity/activereports';
  import { Viewer } from '@grapecity/activereports-vue';
  import '@grapecity/activereports-localization'; //预览汉化
  import '@grapecity/activereports/styles/ar-js-ui.css';
  import '@grapecity/activereports/styles/ar-js-viewer.css';
  import { computed, nextTick, ref } from 'vue';
  import { DocumentCopy, Printer } from '@element-plus/icons-vue';
  import { ElMessage, ElMessageBox } from 'element-plus';

  const visible = ref(false);
  const btnLoading = ref(false);
  let report = {};
  const filename = ref('');
  const reportData = ref(null);
  const reportViewer = ref();

  // 打开报告
  function open(options) {
    visible.value = true;
    filename.value = options.filename || '报告预览';
    reportData.value = options.data;
    
    // 优先使用动态模版JSON，否则使用静态模版
    if (options.templateJson) {
      console.log('Using dynamic template JSON');
      console.log('Template JSON type:', typeof options.templateJson);
      console.log('Template JSON length:', options.templateJson.length);
      console.log('Template JSON first 200 chars:', options.templateJson.substring(0, 200));
      try {
        const parsedTemplate = JSON.parse(options.templateJson);
        console.log('Parsed template structure:', Object.keys(parsedTemplate));
        
        // 检查是否为标准RDLX-JSON格式
        if (parsedTemplate.$schema || parsedTemplate.Type === 'report') {
          console.log('Using standard RDLX-JSON format');
          report = parsedTemplate;
        } else {
          console.log('Converting custom format to ActiveReports format');
          // 将自定义格式转换为ActiveReports可识别的格式
          report = convertToActiveReportsFormat(parsedTemplate);
        }
        
        openReport(report);
      } catch (error) {
        console.error('Failed to parse template JSON:', error);
        console.error('JSON content that failed to parse:', options.templateJson.substring(0, 500));
        ElMessage.error('模版格式错误：' + error.message);
        return;
      }
    } else if (options.template) {
      // 直接传入模版对象
      report = options.template;
      openReport(report);
    } else {
      // 加载静态模版文件
      loadAndOpenReport(options.rptPath || '/diagnosis-templates/report.rdlx-json');
    }
  }

  // 从文件加载并打开报告
  async function loadAndOpenReport(rptPath) {
    try {
      const reportResponse = await fetch(rptPath);
      if (!reportResponse.ok) {
        throw new Error(`Failed to load template: ${reportResponse.status}`);
      }
      const text = await reportResponse.text();
      report = JSON.parse(text);
      openReport(report);
    } catch (error) {
      console.error('Failed to load report template:', error);
      ElMessage.error('模版加载失败：' + error.message);
    }
  }

  // 打开报告
  async function openReport(reportTemplate) {
    try {
      // 注册字体
      await Core.FontStore.registerFonts('/admin/fonts/fontsConfig.json');
      
      // 绑定数据
      if (reportData.value && reportTemplate.DataSources && reportTemplate.DataSources[0]) {
        const dataSourceName = reportTemplate.DataSources[0].Name || 'JsonDataSource';
        console.log('Binding data to source:', dataSourceName);
        reportTemplate.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(reportData.value);
        console.log('Final connection string length:', reportTemplate.DataSources[0].ConnectionProperties.ConnectString.length);
      }
      
      console.log('Final report template structure:', reportTemplate);
      console.log('Report data to bind:', reportData.value);
      console.log('Data fields available:', Object.keys(reportData.value || {}));
      if (reportData.value?.study) {
        console.log('Study fields available:', Object.keys(reportData.value.study));
      }
      if (reportData.value?.diagnosis) {
        console.log('Diagnosis fields available:', Object.keys(reportData.value.diagnosis));
      }
      
      nextTick(async () => {
        if (reportViewer.value) {
          const viewer = reportViewer.value.Viewer();
          viewer.resetDocument();
          viewer.availableExports = ['pdf'];
          
          // 使用PageReport来处理页面报告格式
          if (reportTemplate.Body && reportTemplate.Body.ReportItems) {
            console.log('Using PageReport for custom format');
            const pageReport = new Core.PageReport();
            await pageReport.load(reportTemplate);
            const doc = await pageReport.run();
            viewer.open(doc);
          } else {
            console.log('Using standard viewer.open');
            viewer.open(reportTemplate);
          }
          
          viewer.viewMode = 'Continuous';
          //viewer.toggleSidebar(); //隐藏侧边栏
        }
      });
    } catch (error) {
      console.error('Failed to open report:', error);
      console.error('Template that failed:', reportTemplate);
      ElMessage.error('报告打开失败：' + error.message);
    }
  }

  // 导出PDF
  function exportPDF() {
    ElMessageBox.confirm('确定要导出PDF文件吗？', '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }).then(() => {
      btnLoading.value = true;
      
      const PDF = PdfExport;
      const settings = {
        info: {
          title: filename.value,
          author: 'PACS系统',
        },
        pdfVersion: '1.7',
      };

      const pageReport = new Core.PageReport();
      pageReport
        .load(report)
        .then(() => {
          return pageReport.run();
        })
        .then(pageDocument => {
          return PDF.exportDocument(pageDocument, settings);
        })
        .then(result => {
          result.download(filename.value + '.pdf');
          ElMessage.success('导出成功');
          btnLoading.value = false;
        })
        .catch(error => {
          console.error('Export failed:', error);
          ElMessage.error('导出失败：' + error.message);
          btnLoading.value = false;
        });
    });
  }

  // 打印报告
  async function printReport() {
    try {
      btnLoading.value = true;
      const pageReport = new Core.PageReport();
      await pageReport.load(report);
      const doc = await pageReport.run();
      doc.print();
      ElMessage.success('打印成功');
      btnLoading.value = false;
    } catch (error) {
      console.error('Print failed:', error);
      ElMessage.error('打印失败：' + error.message);
      btnLoading.value = false;
    }
  }

  // 转换自定义格式为ActiveReports标准格式
  function convertToActiveReportsFormat(customTemplate) {
    console.log('Converting template format...');
    
    // 尝试直接使用原格式，让ActiveReports自动处理
    if (customTemplate.Name && customTemplate.DataSources) {
      console.log('Template appears to be in a compatible format, using as-is');
      return customTemplate;
    }
    
    // 如果格式不兼容，返回一个基础的模版
    console.warn('Unknown template format, using fallback template');
    return {
      "$schema": "https://www.grapecity.com/activereportsjs/schemas/report-15.json",
      "Type": "report",
      "Version": "15.1.0",
      "Name": "FallbackTemplate",
      "Body": {
        "Type": "bandedReportItem",
        "Name": "body",
        "Height": "11in",
        "Sections": [
          {
            "Type": "detail",
            "Name": "detail",
            "Height": "8in",
            "Items": [
              {
                "Type": "textBox",
                "Name": "message",
                "Value": "模版格式转换中，请联系管理员更新模版格式",
                "Style": {
                  "fontSize": "14pt",
                  "textAlign": "center"
                },
                "Location": "1in, 2in",
                "Size": "6in, 1in"
              }
            ]
          }
        ]
      },
      "DataSources": [
        {
          "Name": "reportData",
          "ConnectionProperties": {
            "DataProvider": "JSON",
            "ConnectString": "jsondata="
          }
        }
      ]
    };
  }

  const getTitle = computed(() => '预览报表-' + filename.value);

  defineExpose({
    open,
  });
</script>

<style src="@grapecity/activereports/styles/ar-js-ui.css"></style>
<style src="@grapecity/activereports/styles/ar-js-viewer.css"></style>
<style scoped>
  .report-dialog :deep(.el-dialog) {
    margin-top: 1px;
    max-height: 99vh;
  }

  .report-dialog :deep(.el-dialog__body) {
    padding: 0;
    height: calc(100vh - 120px);
  }

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .dialog-header .toolbar-buttons {
    display: flex;
    gap: 8px;
  }

  .viewer-host {
    width: 100%;
    height: calc(100vh - 120px);
  }
</style>