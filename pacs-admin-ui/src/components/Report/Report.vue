<template>
  <el-dialog
    v-model="visible"
    :title="getTitle"
    width="90%"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    append-to-body
    class="report-dialog"
  >
    <template #header>
      <div class="dialog-header">
        <span>{{ getTitle }}</span>
        <div class="toolbar-buttons">
          <el-button
            type="success"
            :icon="DocumentCopy"
            @click="expPDF()"
            :loading="state.btnLoading"
            size="small"
          >
            导出PDF
          </el-button>
          <el-button
            type="danger"
            :icon="Printer"
            @click="print(state.options, true)"
            :loading="state.btnLoading"
            size="small"
          >
            打印
          </el-button>
        </div>
      </div>
    </template>
    <div class="viewer-host">
      <ReportViewer ref="reportViewer" language="zh" />
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
  // 报表查看器包
  import { Core, PdfExport } from '@grapecity/activereports';
  import { Viewer } from '@grapecity/activereports-vue';
  import '@grapecity/activereports-localization'; //预览汉化
  import '@grapecity/activereports/styles/ar-js-ui.css';
  import '@grapecity/activereports/styles/ar-js-viewer.css';
  import { computed, ref, nextTick, reactive } from 'vue';
  import { DocumentCopy, Printer } from '@element-plus/icons-vue';
  import { ElMessage, ElMessageBox } from 'element-plus';

  // 组件注册
  import { defineComponent } from 'vue';
  const ReportViewer = Viewer;

  const state = reactive({
    visible: false,
    report: null,
    btnLoading: false,
    filename: '', //文件名称
    options: {
      filename: '', //文件名称
      rptPath: '/admin/report.rdlx-json', //报表模板文件路径
      templateJson: null, //动态模版JSON
      data: null, //报表数据
    },
  });

  const visible = ref(false);
  const reportViewer = ref();

  async function loadReport(rptPath, templateJson) {
    // 支持从模版JSON直接加载或从文件路径加载
    if (templateJson) {
      console.log('Loading report template from JSON:', templateJson.substring(0, 200));
      return JSON.parse(templateJson);
    } else {
      //从文件加载定义报表
      console.log('Loading report template from:', rptPath);
      console.log('Current location:', window.location.href);
      const reportResponse = await fetch(rptPath);
      console.log('Response status:', reportResponse.status);
      console.log('Response headers:', reportResponse.headers);
      const text = await reportResponse.text();
      console.log('Response text preview:', text.substring(0, 200));
      return JSON.parse(text);
    }
  }

  async function expExcel(options, flag) {
    ElMessageBox.confirm('确定要导出Excel文件吗？', '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }).then(() => {
      state.btnLoading = true;
      doExpExcel(options, flag);
    });
  }

  async function doExpExcel(options, flag) {
    var pageReport = new Core.PageReport();
    var Excel = XlsxExport;
    var settings = {};
    if (flag) {
      settings = {
        sheetName: state.options.filename,
        pageSettings: {
          size: 'A4',
          orientation: 'portrait',
        },
      };
      var pageReport = new Core.PageReport();
      pageReport
        .load(state.report)
        .then(() => {
          return pageReport.run();
        })
        .then(pageDocument => {
          return Excel.exportDocument(pageDocument, settings);
        })
        .then(result => {
          result.download(state.options.filename);
          ElMessage.success('导出成功');
          state.btnLoading = false;
        });
    } else {
      await Core.FontStore.registerFonts('/admin/fonts/fontsConfig.json');
      var rpt = await loadReport(options.rptPath, options.templateJson);
      rpt.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(options.data);
      settings = {
        sheetName: options.filename,
        pageSettings: {
          size: 'A4',
          orientation: 'portrait',
        },
      };

      pageReport
        .load(rpt)
        .then(() => {
          return pageReport.run();
        })
        .then(pageDocument => {
          return Excel.exportDocument(pageDocument, settings);
        })
        .then(result => {
          result.download(options.filename);
          ElMessage.success('导出成功');
          state.btnLoading = false;
        });
    }
  }

  function expPDF() {
    ElMessageBox.confirm('确定要导出PDF文件吗？', '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }).then(() => {
      var PDF = PdfExport;
      var settings = {
        info: {
          title: state.options.filename,
          author: 'pacs-system',
        },
        pdfVersion: '1.7',
      };

      var pageReport = new Core.PageReport();
      state.btnLoading = true;
      pageReport
        .load(state.report)
        .then(() => {
          return pageReport.run();
        })
        .then(pageDocument => {
          return PDF.exportDocument(pageDocument, settings);
        })
        .then(result => {
          result.download(state.options.filename);
          ElMessage.success('导出成功');
          state.btnLoading = false;
        });
    });
  }

  async function print(options, flag) {
    var pageReport = new Core.PageReport();
    if (flag) {
      await pageReport.load(state.report);
      const doc = await pageReport.run();
      doc.print();
      ElMessage.success('打印成功');
    } else {
      await Core.FontStore.registerFonts('/admin/fonts/fontsConfig.json');
      var rpt = await loadReport(options.rptPath, options.templateJson);
      rpt.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(options.data);
      var settings = {
        info: {
          title: 'rpt_print',
          author: 'pacs',
        },
        pdfVersion: '1.7',
      };

      pageReport
        .load(rpt)
        .then(() => {
          return pageReport.run();
        })
        .then(pageDocument => {
          return PdfExport.exportDocument(pageDocument, settings);
        })
        .then(result => {
          const url = URL.createObjectURL(result.data);
          const printWindow = window.open(url);
          printWindow.print();
        });
    }
  }

  async function open(options) {
    console.log('Opening report with options:', options);
    state.options = options;
    state.filename = options.filename || '报告预览';
    
    try {
      // 注册字体
      await Core.FontStore.registerFonts('/admin/fonts/fontsConfig.json');
      
      // 获取报告模版
      let reportTemplate;
      if (options.templateJson) {
        // 使用动态模版JSON
        console.log('Using dynamic template JSON');
        reportTemplate = JSON.parse(options.templateJson);
      } else {
        // 使用静态模版文件
        console.log('Loading static template from:', options.rptPath);
        reportTemplate = await loadReport(options.rptPath || '/admin/report.rdlx-json');
      }
      
      // 绑定数据
      if (options.data && reportTemplate.DataSources && reportTemplate.DataSources[0]) {
        reportTemplate.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(options.data);
      }
      
      state.report = reportTemplate;
      visible.value = true;
      
      // 打开报告
      await openReport(state.report);
      
    } catch (error) {
      console.error('Failed to open report:', error);
      ElMessage.error('报告加载失败：' + error.message);
    }
  }

  async function openReport(report) {
    await nextTick(async () => {
      if (reportViewer.value) {
        const viewer = reportViewer.value.Viewer();
        viewer.resetDocument();
        viewer.availableExports = ['pdf'];
        viewer.open(report);
        viewer.viewMode = 'Continuous';
        //viewer.toggleSidebar(); //隐藏侧边栏
      }
    });
  }

  const getTitle = computed(() => `报表预览：${state.filename}`);

  defineExpose({
    open,
  });
</script>
<style src="@grapecity/activereports/styles/ar-js-ui.css"></style>
<style src="@grapecity/activereports/styles/ar-js-viewer.css"></style>
<style scoped>
  .report-dialog :deep(.el-dialog) {
    margin-top: 5vh;
    max-height: 90vh;
  }

  .report-dialog :deep(.el-dialog__body) {
    padding: 0;
    height: calc(85vh - 120px);
  }

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .dialog-header .toolbar-buttons {
    display: flex;
    gap: 8px;
  }

  .viewer-host {
    width: 100%;
    height: calc(85vh - 120px);
  }
</style>
