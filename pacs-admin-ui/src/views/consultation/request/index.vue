<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="申请编号" prop="requestNo">
        <el-input
          v-model="queryParams.requestNo"
          placeholder="请输入申请编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="患者ID" prop="patientId">
        <el-input
          v-model="queryParams.patientId"
          placeholder="请输入患者ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="会诊医生" prop="consultantName">
        <el-input
          v-model="queryParams.consultantName"
          placeholder="请输入会诊医生"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="待处理" value="PENDING" />
          <el-option label="已接受" value="ACCEPTED" />
          <el-option label="已拒绝" value="REJECTED" />
          <el-option label="已完成" value="COMPLETED" />
          <el-option label="已取消" value="CANCELLED" />
        </el-select>
      </el-form-item>
      <el-form-item label="紧急程度" prop="urgencyLevel">
        <el-select v-model="queryParams.urgencyLevel" placeholder="请选择紧急程度" clearable>
          <el-option label="紧急" value="URGENT" />
          <el-option label="普通" value="NORMAL" />
          <el-option label="非紧急" value="LOW" />
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['consultation:request:create']"
        >发起会诊</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['consultation:request:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="consultationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请编号" align="center" prop="requestNo" width="140" />
      <el-table-column label="患者信息" align="center" width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.patientStudy">
            <div>{{ scope.row.patientStudy.patientName }}</div>
            <div style="color: #909399; font-size: 12px;">{{ scope.row.patientStudy.patientId }}</div>
          </div>
          <div v-else>{{ scope.row.patientId }}</div>
        </template>
      </el-table-column>
      <el-table-column label="检查信息" align="center" width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.patientStudy">
            <div>{{ scope.row.patientStudy.modality }} - {{ scope.row.patientStudy.bodyPart }}</div>
            <div style="color: #909399; font-size: 12px;">{{ scope.row.studyId }}</div>
          </div>
          <div v-else>{{ scope.row.studyId }}</div>
        </template>
      </el-table-column>
      <el-table-column label="会诊医生" align="center" prop="consultantName" width="120" />
      <el-table-column label="紧急程度" align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="getUrgencyTagType(scope.row.urgencyLevel)" size="mini">
            {{ getUrgencyText(scope.row.urgencyLevel) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.status)" size="mini">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="requestTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.requestTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="响应时间" align="center" prop="responseTime" width="160">
        <template slot-scope="scope">
          <span v-if="scope.row.responseTime">{{ parseTime(scope.row.responseTime, '{y}-{m}-{d} {h}:{i}') }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['consultation:request:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['consultation:request:edit']"
            v-if="scope.row.status === 'PENDING'"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleCancel(scope.row)"
            v-hasPermi="['consultation:request:cancel']"
            v-if="scope.row.status === 'PENDING' || scope.row.status === 'ACCEPTED'"
          >取消</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['consultation:request:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改会诊申请对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <consultation-form 
        ref="consultationForm"
        :form-data="form"
        :is-edit="isEdit"
        @submit="submitForm"
        @cancel="cancel"
      />
    </el-dialog>

    <!-- 会诊详情对话框 -->
    <el-dialog title="会诊详情" :visible.sync="detailOpen" width="1200px" append-to-body>
      <consultation-detail 
        v-if="detailOpen"
        :consultation-id="currentId"
        @close="detailOpen = false"
      />
    </el-dialog>

    <!-- 取消会诊对话框 -->
    <el-dialog title="取消会诊申请" :visible.sync="cancelOpen" width="500px" append-to-body>
      <el-form ref="cancelForm" :model="cancelForm" label-width="100px">
        <el-form-item label="取消原因" prop="cancelReason">
          <el-input
            v-model="cancelForm.cancelReason"
            type="textarea"
            :rows="4"
            placeholder="请输入取消原因"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelOpen = false">取 消</el-button>
        <el-button type="primary" @click="confirmCancel">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMyRequests, getConsultationRequest, addConsultationRequest, updateConsultationRequest, delConsultationRequest, cancelConsultation } from "@/api/consultation/request";
import ConsultationForm from "../components/ConsultationForm";
import ConsultationDetail from "../components/ConsultationDetail";

export default {
  name: "ConsultationRequest",
  components: {
    ConsultationForm,
    ConsultationDetail
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 会诊申请表格数据
      consultationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 是否显示取消弹出层
      cancelOpen: false,
      // 是否编辑
      isEdit: false,
      // 当前ID
      currentId: null,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        requestNo: null,
        patientId: null,
        consultantName: null,
        status: null,
        urgencyLevel: null
      },
      // 表单参数
      form: {},
      // 取消表单
      cancelForm: {
        cancelReason: ""
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询会诊申请列表 */
    getList() {
      this.loading = true;
      listMyRequests(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.consultationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        requestNo: null,
        patientId: null,
        studyId: null,
        checkId: null,
        consultantId: null,
        requestReason: null,
        requestDescription: null,
        urgencyLevel: "NORMAL",
        expectedCompletionTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "发起会诊申请";
      this.isEdit = false;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getConsultationRequest(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改会诊申请";
        this.isEdit = true;
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.currentId = row.id;
      this.detailOpen = true;
    },
    /** 取消按钮操作 */
    handleCancel(row) {
      this.currentId = row.id;
      this.cancelForm.cancelReason = "";
      this.cancelOpen = true;
    },
    /** 确认取消 */
    confirmCancel() {
      cancelConsultation(this.currentId, this.cancelForm).then(response => {
        this.$modal.msgSuccess("取消成功");
        this.cancelOpen = false;
        this.getList();
      });
    },
    /** 提交按钮 */
    submitForm(form) {
      if (this.isEdit) {
        updateConsultationRequest(form).then(response => {
          this.$modal.msgSuccess("修改成功");
          this.open = false;
          this.getList();
        });
      } else {
        addConsultationRequest(form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除会诊申请编号为"' + ids + '"的数据项？').then(function() {
        return delConsultationRequest(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 获取紧急程度标签类型 */
    getUrgencyTagType(urgencyLevel) {
      const tagTypeMap = {
        'URGENT': 'danger',
        'NORMAL': 'warning',
        'LOW': 'info'
      };
      return tagTypeMap[urgencyLevel] || 'info';
    },
    /** 获取紧急程度文本 */
    getUrgencyText(urgencyLevel) {
      const textMap = {
        'URGENT': '紧急',
        'NORMAL': '普通',
        'LOW': '非紧急'
      };
      return textMap[urgencyLevel] || urgencyLevel;
    },
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const tagTypeMap = {
        'PENDING': 'warning',
        'ACCEPTED': 'primary',
        'REJECTED': 'danger',
        'COMPLETED': 'success',
        'CANCELLED': 'info'
      };
      return tagTypeMap[status] || 'info';
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const textMap = {
        'PENDING': '待处理',
        'ACCEPTED': '已接受',
        'REJECTED': '已拒绝',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消'
      };
      return textMap[status] || status;
    }
  }
};
</script>
