<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon pending">
              <i class="el-icon-time"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.pendingCount || 0 }}</div>
              <div class="statistics-label">待处理</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon accepted">
              <i class="el-icon-check"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.acceptedCount || 0 }}</div>
              <div class="statistics-label">已接受</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon completed">
              <i class="el-icon-success"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.completedCount || 0 }}</div>
              <div class="statistics-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon total">
              <i class="el-icon-document"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.totalCount || 0 }}</div>
              <div class="statistics-label">总计</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 查询表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="申请编号" prop="requestNo">
        <el-input
          v-model="queryParams.requestNo"
          placeholder="请输入申请编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请医生" prop="requesterName">
        <el-input
          v-model="queryParams.requesterName"
          placeholder="请输入申请医生"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="会诊医生" prop="consultantName">
        <el-input
          v-model="queryParams.consultantName"
          placeholder="请输入会诊医生"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="待处理" value="PENDING" />
          <el-option label="已接受" value="ACCEPTED" />
          <el-option label="已拒绝" value="REJECTED" />
          <el-option label="已完成" value="COMPLETED" />
          <el-option label="已取消" value="CANCELLED" />
        </el-select>
      </el-form-item>
      <el-form-item label="紧急程度" prop="urgencyLevel">
        <el-select v-model="queryParams.urgencyLevel" placeholder="请选择紧急程度" clearable>
          <el-option label="紧急" value="URGENT" />
          <el-option label="普通" value="NORMAL" />
          <el-option label="非紧急" value="LOW" />
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-time"
          size="mini"
          @click="handleExpiredConsultations"
          v-hasPermi="['consultation:management:handle']"
        >处理过期申请</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-message"
          size="mini"
          @click="sendReminders"
          v-hasPermi="['consultation:management:handle']"
        >发送提醒</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['consultation:management:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="consultationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请编号" align="center" prop="requestNo" width="140" />
      <el-table-column label="患者姓名" align="center" prop="patientStudy.patientName" width="100" />
      <el-table-column label="检查号" align="center" prop="studyId" width="120" />
      <el-table-column label="申请医生" align="center" prop="requesterName" width="100" />
      <el-table-column label="会诊医生" align="center" prop="consultantName" width="100" />
      <el-table-column label="紧急程度" align="center" prop="urgencyLevel" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.urgencyLevel === 'URGENT'" type="danger" size="mini">紧急</el-tag>
          <el-tag v-else-if="scope.row.urgencyLevel === 'NORMAL'" type="primary" size="mini">普通</el-tag>
          <el-tag v-else-if="scope.row.urgencyLevel === 'LOW'" type="info" size="mini">非紧急</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 'PENDING'" type="warning" size="mini">待处理</el-tag>
          <el-tag v-else-if="scope.row.status === 'ACCEPTED'" type="primary" size="mini">已接受</el-tag>
          <el-tag v-else-if="scope.row.status === 'REJECTED'" type="danger" size="mini">已拒绝</el-tag>
          <el-tag v-else-if="scope.row.status === 'COMPLETED'" type="success" size="mini">已完成</el-tag>
          <el-tag v-else-if="scope.row.status === 'CANCELLED'" type="info" size="mini">已取消</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="requestTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.requestTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="响应时间" align="center" prop="responseTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.responseTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="完成时间" align="center" prop="completionTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.completionTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请原因" align="center" prop="requestReason" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['consultation:management:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handleAuditLogs(scope.row)"
            v-hasPermi="['consultation:management:query']"
          >日志</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['consultation:management:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 操作日志对话框 -->
    <el-dialog title="操作日志" :visible.sync="auditLogDialogVisible" width="800px" append-to-body>
      <el-timeline>
        <el-timeline-item
          v-for="log in auditLogs"
          :key="log.id"
          :timestamp="parseTime(log.operationTime, '{y}-{m}-{d} {h}:{i}')"
          placement="top">
          <el-card>
            <h4>{{ log.operationDescription }}</h4>
            <p>操作人：{{ log.operationUserName }}</p>
            <p>操作IP：{{ log.operationIp }}</p>
            <p v-if="log.oldStatus && log.newStatus">状态变更：{{ log.oldStatus }} → {{ log.newStatus }}</p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditLogDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listConsultationRequest, getConsultationRequest, delConsultationRequest, getConsultationStatistics, getConsultationAuditLogs, handleExpiredConsultations, sendConsultationReminders } from "@/api/consultation/request";

export default {
  name: "ConsultationManagement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 会诊申请表格数据
      consultationList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        requestNo: null,
        requesterName: null,
        consultantName: null,
        status: null,
        urgencyLevel: null,
      },
      // 日期范围
      dateRange: [],
      // 统计数据
      statistics: {},
      // 操作日志对话框
      auditLogDialogVisible: false,
      auditLogs: []
    };
  },
  created() {
    this.getList();
    this.getStatistics();
  },
  methods: {
    /** 查询会诊申请列表 */
    getList() {
      this.loading = true;
      listConsultationRequest(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.consultationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取统计数据 */
    getStatistics() {
      getConsultationStatistics().then(response => {
        this.statistics = response.data;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.$router.push({
        path: '/consultation/management/detail',
        query: { id: row.id }
      });
    },
    /** 操作日志按钮操作 */
    handleAuditLogs(row) {
      getConsultationAuditLogs(row.id).then(response => {
        this.auditLogs = response.data || [];
        this.auditLogDialogVisible = true;
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除会诊申请编号为"' + (row.requestNo || ids) + '"的数据项？').then(function() {
        return delConsultationRequest(ids);
      }).then(() => {
        this.getList();
        this.getStatistics();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 处理过期申请 */
    handleExpiredConsultations() {
      this.$modal.confirm('是否确认处理过期的会诊申请？').then(function() {
        return handleExpiredConsultations();
      }).then((response) => {
        this.$modal.msgSuccess(response.msg);
        this.getList();
        this.getStatistics();
      }).catch(() => {});
    },
    /** 发送提醒 */
    sendReminders() {
      this.$modal.confirm('是否确认发送会诊提醒？').then(function() {
        return sendConsultationReminders();
      }).then((response) => {
        this.$modal.msgSuccess(response.msg);
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('consultation/request/export', {
        ...this.queryParams
      }, `consultation_request_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.statistics-card {
  margin-bottom: 20px;
}

.statistics-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.statistics-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.statistics-icon.pending {
  background: #e6a23c;
}

.statistics-icon.accepted {
  background: #409eff;
}

.statistics-icon.completed {
  background: #67c23a;
}

.statistics-icon.total {
  background: #909399;
}

.statistics-info {
  flex: 1;
}

.statistics-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.statistics-label {
  font-size: 14px;
  color: #606266;
  margin-top: 8px;
}

.mb20 {
  margin-bottom: 20px;
}
</style>
