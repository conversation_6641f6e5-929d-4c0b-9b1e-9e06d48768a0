<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="申请编号" prop="requestNo">
        <el-input
          v-model="queryParams.requestNo"
          placeholder="请输入申请编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请医生" prop="requesterName">
        <el-input
          v-model="queryParams.requesterName"
          placeholder="请输入申请医生"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="患者ID" prop="patientId">
        <el-input
          v-model="queryParams.patientId"
          placeholder="请输入患者ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="待处理" value="PENDING" />
          <el-option label="已接受" value="ACCEPTED" />
          <el-option label="已拒绝" value="REJECTED" />
          <el-option label="已完成" value="COMPLETED" />
        </el-select>
      </el-form-item>
      <el-form-item label="紧急程度" prop="urgencyLevel">
        <el-select v-model="queryParams.urgencyLevel" placeholder="请选择紧急程度" clearable>
          <el-option label="紧急" value="URGENT" />
          <el-option label="普通" value="NORMAL" />
          <el-option label="非紧急" value="LOW" />
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-check"
          size="mini"
          :disabled="single"
          @click="handleBatchAccept"
          v-hasPermi="['consultation:consultant:handle']"
        >批量接受</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-view"
          size="mini"
          @click="handlePendingOnly"
        >仅显示待处理</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="consultationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请编号" align="center" prop="requestNo" width="140" />
      <el-table-column label="申请医生" align="center" prop="requesterName" width="120" />
      <el-table-column label="患者信息" align="center" width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.patientStudy">
            <div>{{ scope.row.patientStudy.patientName }}</div>
            <div style="color: #909399; font-size: 12px;">{{ scope.row.patientStudy.patientId }}</div>
          </div>
          <div v-else>{{ scope.row.patientId }}</div>
        </template>
      </el-table-column>
      <el-table-column label="检查信息" align="center" width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.patientStudy">
            <div>{{ scope.row.patientStudy.modality }} - {{ scope.row.patientStudy.bodyPart }}</div>
            <div style="color: #909399; font-size: 12px;">{{ scope.row.studyId }}</div>
          </div>
          <div v-else>{{ scope.row.studyId }}</div>
        </template>
      </el-table-column>
      <el-table-column label="申请原因" align="center" prop="requestReason" width="200" show-overflow-tooltip />
      <el-table-column label="紧急程度" align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="getUrgencyTagType(scope.row.urgencyLevel)" size="mini">
            {{ getUrgencyText(scope.row.urgencyLevel) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.status)" size="mini">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="requestTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.requestTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="期望完成时间" align="center" prop="expectedCompletionTime" width="160">
        <template slot-scope="scope">
          <span v-if="scope.row.expectedCompletionTime">{{ parseTime(scope.row.expectedCompletionTime, '{y}-{m}-{d} {h}:{i}') }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['consultation:consultant:handle']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleAccept(scope.row)"
            v-hasPermi="['consultation:consultant:handle']"
            v-if="scope.row.status === 'PENDING'"
          >接受</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleReject(scope.row)"
            v-hasPermi="['consultation:consultant:handle']"
            v-if="scope.row.status === 'PENDING'"
          >拒绝</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleConsult(scope.row)"
            v-hasPermi="['consultation:consultant:handle']"
            v-if="scope.row.status === 'ACCEPTED'"
          >会诊</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 会诊详情对话框 -->
    <el-dialog title="会诊详情" :visible.sync="detailOpen" width="1200px" append-to-body>
      <consultation-detail 
        v-if="detailOpen"
        :consultation-id="currentId"
        :show-actions="true"
        @close="detailOpen = false"
        @refresh="getList"
      />
    </el-dialog>

    <!-- 接受会诊对话框 -->
    <el-dialog title="接受会诊申请" :visible.sync="acceptOpen" width="500px" append-to-body>
      <el-form ref="acceptForm" :model="acceptForm" label-width="100px">
        <el-form-item label="接受说明" prop="acceptReason">
          <el-input
            v-model="acceptForm.acceptReason"
            type="textarea"
            :rows="4"
            placeholder="请输入接受说明（可选）"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="acceptOpen = false">取 消</el-button>
        <el-button type="primary" @click="confirmAccept">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 拒绝会诊对话框 -->
    <el-dialog title="拒绝会诊申请" :visible.sync="rejectOpen" width="500px" append-to-body>
      <el-form ref="rejectForm" :model="rejectForm" :rules="rejectRules" label-width="100px">
        <el-form-item label="拒绝原因" prop="rejectReason">
          <el-input
            v-model="rejectForm.rejectReason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝原因"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="rejectOpen = false">取 消</el-button>
        <el-button type="primary" @click="confirmReject">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 会诊诊断对话框 -->
    <el-dialog title="会诊诊断" :visible.sync="consultOpen" width="1200px" append-to-body>
      <consultation-diagnose 
        v-if="consultOpen"
        :consultation-id="currentId"
        @close="consultOpen = false"
        @refresh="getList"
      />
    </el-dialog>
  </div>
</template>

<script>
import { listMyConsultations, acceptConsultation, rejectConsultation } from "@/api/consultation/request";
import ConsultationDetail from "../components/ConsultationDetail";
import ConsultationDiagnose from "../components/ConsultationDiagnose";

export default {
  name: "ConsultationConsultant",
  components: {
    ConsultationDetail,
    ConsultationDiagnose
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 会诊申请表格数据
      consultationList: [],
      // 是否显示详情弹出层
      detailOpen: false,
      // 是否显示接受弹出层
      acceptOpen: false,
      // 是否显示拒绝弹出层
      rejectOpen: false,
      // 是否显示会诊弹出层
      consultOpen: false,
      // 当前ID
      currentId: null,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        requestNo: null,
        requesterName: null,
        patientId: null,
        status: null,
        urgencyLevel: null
      },
      // 接受表单
      acceptForm: {
        acceptReason: ""
      },
      // 拒绝表单
      rejectForm: {
        rejectReason: ""
      },
      // 拒绝表单验证规则
      rejectRules: {
        rejectReason: [
          { required: true, message: "拒绝原因不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询会诊申请列表 */
    getList() {
      this.loading = true;
      listMyConsultations(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.consultationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 仅显示待处理 */
    handlePendingOnly() {
      this.queryParams.status = this.queryParams.status === 'PENDING' ? null : 'PENDING';
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.currentId = row.id;
      this.detailOpen = true;
    },
    /** 接受按钮操作 */
    handleAccept(row) {
      this.currentId = row.id;
      this.acceptForm.acceptReason = "";
      this.acceptOpen = true;
    },
    /** 确认接受 */
    confirmAccept() {
      acceptConsultation(this.currentId, this.acceptForm).then(response => {
        this.$modal.msgSuccess("接受成功");
        this.acceptOpen = false;
        this.getList();
      });
    },
    /** 批量接受 */
    handleBatchAccept() {
      if (this.ids.length === 0) {
        this.$modal.msgWarning("请选择要接受的会诊申请");
        return;
      }
      this.$modal.confirm('是否确认批量接受选中的会诊申请？').then(() => {
        const promises = this.ids.map(id => acceptConsultation(id, { acceptReason: "批量接受" }));
        Promise.all(promises).then(() => {
          this.$modal.msgSuccess("批量接受成功");
          this.getList();
        }).catch(() => {
          this.$modal.msgError("批量接受失败");
        });
      });
    },
    /** 拒绝按钮操作 */
    handleReject(row) {
      this.currentId = row.id;
      this.rejectForm.rejectReason = "";
      this.rejectOpen = true;
    },
    /** 确认拒绝 */
    confirmReject() {
      this.$refs["rejectForm"].validate(valid => {
        if (valid) {
          rejectConsultation(this.currentId, this.rejectForm).then(response => {
            this.$modal.msgSuccess("拒绝成功");
            this.rejectOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 会诊按钮操作 */
    handleConsult(row) {
      this.currentId = row.id;
      this.consultOpen = true;
    },
    /** 获取紧急程度标签类型 */
    getUrgencyTagType(urgencyLevel) {
      const tagTypeMap = {
        'URGENT': 'danger',
        'NORMAL': 'warning',
        'LOW': 'info'
      };
      return tagTypeMap[urgencyLevel] || 'info';
    },
    /** 获取紧急程度文本 */
    getUrgencyText(urgencyLevel) {
      const textMap = {
        'URGENT': '紧急',
        'NORMAL': '普通',
        'LOW': '非紧急'
      };
      return textMap[urgencyLevel] || urgencyLevel;
    },
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const tagTypeMap = {
        'PENDING': 'warning',
        'ACCEPTED': 'primary',
        'REJECTED': 'danger',
        'COMPLETED': 'success',
        'CANCELLED': 'info'
      };
      return tagTypeMap[status] || 'info';
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const textMap = {
        'PENDING': '待处理',
        'ACCEPTED': '已接受',
        'REJECTED': '已拒绝',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消'
      };
      return textMap[status] || status;
    }
  }
};
</script>
