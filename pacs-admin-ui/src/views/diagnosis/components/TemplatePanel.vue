<template>
  <div class="template-panel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <div class="header-title">
        <el-icon class="title-icon">
          <Document/>
        </el-icon>
        <span>诊断模板</span>
      </div>
      <div class="header-actions">
        <el-tooltip content="智能推荐" placement="top">
          <el-button
              type="text"
              :icon="MagicStick"
              @click="getSmartRecommendations"
              :loading="smartLoading"
              size="small"
          />
        </el-tooltip>
        <el-tooltip content="刷新模板" placement="top">
          <el-button
              type="text"
              :icon="Refresh"
              @click="refreshTemplates"
              :loading="loading"
              size="small"
          />
        </el-tooltip>
        <el-tooltip content="新建模板" placement="top">
          <el-button
              type="text"
              :icon="Plus"
              @click="handleAddTemplate"
              size="small"
          />
        </el-tooltip>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-container">
      <el-input
          v-model="searchKeyword"
          placeholder="搜索模板名称、部位、关键词..."
          :prefix-icon="Search"
          size="small"
          clearable
          @input="handleSearch"
      />

      <!-- 快速筛选 -->
      <div class="quick-filters">
        <el-button-group size="small">
          <el-button
              :type="modalityFilter === '' ? 'primary' : ''"
              @click="setModalityFilter('')"
          >
            全部
          </el-button>
          <el-button
              v-for="modality in availableModalities"
              :key="modality"
              :type="modalityFilter === modality ? 'primary' : ''"
              @click="setModalityFilter(modality)"
          >
            {{ modality }}
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 模板树形列表 -->
    <div class="template-tree-container" v-loading="loading">
      <!-- 模板Tab切换 -->
      <el-tabs v-model="activeTab" class="template-tabs">
        <!-- 智能推荐 -->
        <el-tab-pane name="recommend">
          <template #label>
            <span>
              推荐 <el-badge :value="smartRecommendations.length" :max="99" v-if="smartRecommendations.length > 0"/>
            </span>
          </template>
          <div class="template-list">
            <div v-if="smartRecommendations.length === 0" class="empty-recommend">
              <el-empty description="暂无推荐模板" :image-size="80">
                <template #description>
                  <p>根据当前患者信息暂无匹配的推荐模板</p>
                </template>
              </el-empty>
            </div>
            <div
                v-for="template in smartRecommendations"
                :key="`smart-${template.id}`"
                class="template-card smart-card"
                @click="selectTemplate(template)"
            >
              <div class="template-content">
                <div class="template-header">
                  <span class="template-name">{{ template.name }}</span>
                  <div class="template-tags">
                    <el-tag size="small" plain>{{ template.modalityType }}</el-tag>
                    <el-tag v-if="template.isDefault === '1'" type="warning" size="small" plain>默认</el-tag>
                  </div>
                </div>
                <div class="template-meta">
                  <div style="display: flex;justify-content: space-between">
                    <span class="meta-item">
                      <el-icon><Location/></el-icon>
                      {{ template.bodyPart }}
                    </span>
                    <span class="meta-item">
                      <el-icon><DataAnalysis/></el-icon>
                      匹配度 {{ getMatchScore(template) }}%
                    </span>
                    <span class="meta-item">
                      <el-icon><Document/></el-icon>
                      使用 {{ template.usageCount || 0 }} 次
                    </span>
                </div>
                </div>
                <div class="template-preview" v-if="template.findings || template.opinion">
                  <div class="preview-content">
                    {{ truncateText(template.findings || template.opinion, 50) }}
                  </div>
                </div>
              </div>
              <div class="template-actions">
                <el-icon class="apply-icon">
                  <Check/>
                </el-icon>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 私有模板 -->
        <el-tab-pane name="private">
          <template #label>
            <span>
              <el-icon><User/></el-icon>
              私有 <el-badge :value="privateTemplates.length" :max="99" v-if="privateTemplates.length > 0"/>
            </span>
          </template>
          <div class="template-list">
            <div
                v-for="template in privateTemplates"
                :key="template.id"
                class="template-card"
                @click="selectTemplate(template)"
            >
              <div class="template-content">
                <div class="template-header">
                  <span class="template-name">{{ template.name }}</span>
                  <div class="template-tags">
                    <el-tag :type="getModalityTagType(template.modalityType)" size="small">
                      {{ template.modalityType }}
                    </el-tag>
                    <el-tag v-if="template.isDefault === '1'" type="warning" size="small">默认</el-tag>
                  </div>
                </div>
                <div class="template-meta">
                  <span class="meta-item">{{ template.bodyPart }}</span>
                  <span class="meta-item">使用{{ template.usageCount || 0 }}次</span>
                </div>
                <div class="template-preview">
                  <div v-if="template.findings" class="preview-section">
                    <span class="preview-label">所见:</span>
                    <span class="preview-text">{{ truncateText(template.findings, 30) }}</span>
                  </div>
                  <div v-if="template.opinion" class="preview-section">
                    <span class="preview-label">意见:</span>
                    <span class="preview-text">{{ truncateText(template.opinion, 30) }}</span>
                  </div>
                </div>
              </div>
              <div class="template-actions">
                <el-dropdown @command="handleTemplateAction" trigger="click" @click.stop>
                  <el-icon class="action-icon">
                    <MoreFilled/>
                  </el-icon>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{action: 'apply', template}">
                        <el-icon>
                          <Check/>
                        </el-icon>
                        应用
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'preview', template}">
                        <el-icon>
                          <View/>
                        </el-icon>
                        预览
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'edit', template}">
                        <el-icon>
                          <Edit/>
                        </el-icon>
                        编辑
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'copy', template}">
                        <el-icon>
                          <Files/>
                        </el-icon>
                        复制
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'delete', template}" divided>
                        <el-icon>
                          <Delete/>
                        </el-icon>
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 公共模板 -->
        <el-tab-pane name="public">
          <template #label>
            <span>
              <el-icon><OfficeBuilding/></el-icon>
              公有 <el-badge :value="publicTemplates.length" :max="99" v-if="publicTemplates.length > 0"/>
            </span>
          </template>
          <div class="template-list">
            <div
                v-for="template in publicTemplates"
                :key="template.id"
                class="template-card"
                @click="selectTemplate(template)"
            >
              <div class="template-content">
                <div class="template-header">
                  <span class="template-name">{{ template.name }}</span>
                  <div class="template-tags">
                    <el-tag :type="getModalityTagType(template.modalityType)" size="small">
                      {{ template.modalityType }}
                    </el-tag>
                    <el-tag v-if="template.isDefault === '1'" type="warning" size="small">默认</el-tag>
                  </div>
                </div>
                <div class="template-meta">
                  <span class="meta-item">{{ template.bodyPart }}</span>
                  <span class="meta-item">使用{{ template.usageCount || 0 }}次</span>
                </div>
                <div class="template-preview">
                  <div v-if="template.findings" class="preview-section">
                    <span class="preview-label">所见:</span>
                    <span class="preview-text">{{ truncateText(template.findings, 30) }}</span>
                  </div>
                  <div v-if="template.opinion" class="preview-section">
                    <span class="preview-label">意见:</span>
                    <span class="preview-text">{{ truncateText(template.opinion, 30) }}</span>
                  </div>
                </div>
              </div>
              <div class="template-actions">
                <el-dropdown @command="handleTemplateAction" trigger="click" @click.stop>
                  <el-icon class="action-icon">
                    <MoreFilled/>
                  </el-icon>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{action: 'apply', template}">
                        <el-icon>
                          <Check/>
                        </el-icon>
                        应用
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'preview', template}">
                        <el-icon>
                          <View/>
                        </el-icon>
                        预览
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'copy', template}">
                        <el-icon>
                          <Files/>
                        </el-icon>
                        复制
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 空状态 -->
      <div v-if="allTemplates.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无模板数据">
          <el-button type="primary" @click="handleAddTemplate">创建第一个模板</el-button>
        </el-empty>
      </div>
    </div>

    <!-- 模板预览对话框 -->
    <el-dialog
        v-model="previewDialogVisible"
        title="模板预览"
        width="60%"
        :before-close="handlePreviewClose"
    >
      <div v-if="currentPreviewTemplate" class="template-preview-dialog">
        <div class="preview-header">
          <h3>{{ currentPreviewTemplate.name }}</h3>
          <div class="preview-meta">
            <el-tag :type="getModalityTagType(currentPreviewTemplate.modalityType)" size="small">
              {{ currentPreviewTemplate.modalityType }}
            </el-tag>
            <span class="preview-part">{{ currentPreviewTemplate.bodyPart }}</span>
          </div>
        </div>

        <div class="preview-content-full">
          <div v-if="currentPreviewTemplate.findings" class="preview-section-full">
            <h4>影像所见：</h4>
            <div class="preview-text">{{ currentPreviewTemplate.findings }}</div>
          </div>
          <div v-if="currentPreviewTemplate.opinion" class="preview-section-full">
            <h4>影像意见：</h4>
            <div class="preview-text">{{ currentPreviewTemplate.opinion }}</div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="previewDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="applyPreviewTemplate">应用模板</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {ref, computed, onMounted, watch} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {
  Document,
  Refresh,
  Search,
  Plus,
  Edit,
  Delete,
  View,
  MagicStick,
  MoreFilled,
  FolderOpened,
  Files,
  User,
  OfficeBuilding,
  Check,
  Location,
  DataAnalysis
} from '@element-plus/icons-vue'
import {
  listTemplate,
  addTemplate,
  updateTemplate,
  delTemplate,
  getUserTemplates,
  getPublicTemplates
} from '@/api/diagnosis/template'

// Props
const props = defineProps({
  modality: {
    type: String,
    default: ''
  },
  bodyPart: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['template-apply', 'add-template'])

// 响应式数据
const loading = ref(false)
const smartLoading = ref(false)
const searchKeyword = ref('')
const modalityFilter = ref('')
const userTemplates = ref([]) // 用户私有模板
const publicTemplatesData = ref([]) // 公共模板
const smartRecommendations = ref([])
const previewDialogVisible = ref(false)
const currentPreviewTemplate = ref(null)
const templateTreeRef = ref(null)
const activeTab = ref('recommend') // 默认显示推荐模板

// 可用的检查类型
const availableModalities = ref(['CT', 'MRI', 'DR', 'US', 'CR', 'DX'])

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'label'
}

// 私有模板
const privateTemplates = computed(() => {
  let filtered = userTemplates.value
  
  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(keyword) ||
        template.bodyPart.toLowerCase().includes(keyword) ||
        template.modalityType.toLowerCase().includes(keyword) ||
        (template.keywords && template.keywords.toLowerCase().includes(keyword))
    )
  }

  // 检查类型筛选
  if (modalityFilter.value) {
    filtered = filtered.filter(template => template.modalityType === modalityFilter.value)
  }

  console.log('私有模板数量:', filtered.length, '原始用户模板数量:', userTemplates.value.length)
  console.log('私有模板数据:', filtered)
  return filtered
})

// 公共模板
const publicTemplates = computed(() => {
  let filtered = publicTemplatesData.value
  
  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(keyword) ||
        template.bodyPart.toLowerCase().includes(keyword) ||
        template.modalityType.toLowerCase().includes(keyword) ||
        (template.keywords && template.keywords.toLowerCase().includes(keyword))
    )
  }

  // 检查类型筛选
  if (modalityFilter.value) {
    filtered = filtered.filter(template => template.modalityType === modalityFilter.value)
  }

  console.log('公共模板数量:', filtered.length)
  return filtered
})

// 合并所有模板用于智能推荐
const allTemplates = computed(() => {
  return [...userTemplates.value, ...publicTemplatesData.value]
})

// 树形数据结构（暂时保留，但不使用）
const templateTreeData = computed(() => {
  const treeData = []

  // 私有模板分组（放在前面）
  if (privateTemplates.value.length > 0) {
    const privateNode = {
      id: 'private',
      label: '我的模板',
      type: 'category',
      key: 'private',
      count: privateTemplates.value.length,
      isPublic: false,
      children: buildModalityTree(privateTemplates.value)
    }
    treeData.push(privateNode)
  }

  // 公共模板分组
  if (publicTemplates.value.length > 0) {
    const publicNode = {
      id: 'public',
      label: '公共模板',
      type: 'category',
      key: 'public',
      count: publicTemplates.value.length,
      isPublic: true,
      children: buildModalityTree(publicTemplates.value)
    }
    treeData.push(publicNode)
  }

  return treeData
})

// 构建检查类型树形结构
const buildModalityTree = (templates) => {
  const modalityGroups = {}

  templates.forEach(template => {
    const modality = template.modalityType || '其他'
    if (!modalityGroups[modality]) {
      modalityGroups[modality] = []
    }
    modalityGroups[modality].push({
      ...template,
      id: `template-${template.id}`,
      type: 'template'
    })
  })

  return Object.keys(modalityGroups).map(modality => ({
    id: `modality-${modality}`,
    label: modality,
    type: 'modality',
    key: modality,
    count: modalityGroups[modality].length,
    children: modalityGroups[modality].sort((a, b) => {
      // 按使用次数和创建时间排序
      if (b.usageCount !== a.usageCount) {
        return (b.usageCount || 0) - (a.usageCount || 0)
      }
      return new Date(b.createTime || 0) - new Date(a.createTime || 0)
    })
  }))
}

// 获取模板列表
const getTemplates = async () => {
  loading.value = true
  try {
    console.log('开始获取用户模板和公共模板...')
    
    // 并行获取用户模板和公共模板
    const [userRes, publicRes] = await Promise.all([
      getUserTemplates(),
      getPublicTemplates()
    ])

    if (userRes.code === 200) {
      userTemplates.value = userRes.data || []
      console.log('获取到用户私有模板:', userTemplates.value.length, '个')
      console.log('用户模板详情:', userTemplates.value)
    } else {
      console.warn('获取用户模板失败:', userRes.msg)
      userTemplates.value = []
    }

    if (publicRes.code === 200) {
      publicTemplatesData.value = publicRes.data || []
      console.log('获取到公共模板:', publicTemplatesData.value.length, '个')
    } else {
      console.warn('获取公共模板失败:', publicRes.msg)
      publicTemplatesData.value = []
    }

    console.log('模板加载完成 - 用户模板:', userTemplates.value.length, '公共模板:', publicTemplatesData.value.length)

  } catch (error) {
    console.error('获取模板列表失败:', error)
    ElMessage.error('获取模板列表失败')
    userTemplates.value = []
    publicTemplatesData.value = []
  } finally {
    loading.value = false
  }
}

// 智能推荐
const getSmartRecommendations = async () => {
  if (!props.modality && !props.bodyPart) {
    ElMessage.info('请先选择患者以获取智能推荐')
    return
  }

  smartLoading.value = true
  try {
    // 从现有模板中筛选匹配的模板
    const recommendations = allTemplates.value
        .filter(template => {
          // 检查类型匹配或部位匹配
          const modalityMatch = props.modality && template.modalityType === props.modality
          const bodyPartMatch = props.bodyPart && template.bodyPart && template.bodyPart.includes(props.bodyPart)
          return modalityMatch || bodyPartMatch
        })
        .map(template => ({
          ...template,
          matchScore: calculateMatchScore(template)
        }))
        .sort((a, b) => {
          if (b.matchScore !== a.matchScore) {
            return b.matchScore - a.matchScore
          }
          return (b.usageCount || 0) - (a.usageCount || 0)
        })
        .slice(0, 5) // 只显示前5个推荐

    smartRecommendations.value = recommendations

 /*   if (recommendations.length > 0) {
      ElMessage.success(`找到 ${recommendations.length} 个推荐模板`)
    } else {
      ElMessage.info('暂无匹配的推荐模板')
    }*/

  } catch (error) {
    console.error('获取智能推荐失败:', error)
    ElMessage.error('获取智能推荐失败')
  } finally {
    smartLoading.value = false
  }
}

// 计算匹配分数
const calculateMatchScore = (template) => {
  let score = 0

  // 检查类型完全匹配 +50分
  if (props.modality && template.modalityType === props.modality) {
    score += 50
  }

  // 身体部位匹配 +30分
  if (props.bodyPart && template.bodyPart && template.bodyPart.includes(props.bodyPart)) {
    score += 30
  }

  // 默认模板 +10分
  if (template.isDefault === '1') {
    score += 10
  }

  // 使用次数加分（最多10分）
  score += Math.min((template.usageCount || 0) / 10, 10)

  return Math.min(score, 100)
}

// 获取匹配分数
const getMatchScore = (template) => {
  return template.matchScore || calculateMatchScore(template)
}

// 刷新模板
const refreshTemplates = () => {
  getTemplates()
  smartRecommendations.value = []
}

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑在计算属性中处理
}

// 设置检查类型筛选
const setModalityFilter = (modality) => {
  modalityFilter.value = modality
}

// 处理标签页切换
const handleTabChange = (tab) => {
  activeTab.value = tab
}

// 选择模板
const selectTemplate = async (template) => {
  try {
    // 触发应用事件
    emit('template-apply', template)
    ElMessage.success(`已应用模板：${template.name}`)

    // TODO: 后续可以添加使用次数统计功能

  } catch (error) {
    console.error('应用模板失败:', error)
    ElMessage.error('应用模板失败')
  }
}

// 预览模板
const previewTemplate = (template) => {
  currentPreviewTemplate.value = template
  previewDialogVisible.value = true
}

// 应用预览的模板
const applyPreviewTemplate = () => {
  if (currentPreviewTemplate.value) {
    selectTemplate(currentPreviewTemplate.value)
    previewDialogVisible.value = false
  }
}

// 处理模板操作
const handleTemplateAction = async ({action, template}) => {
  switch (action) {
    case 'apply':
      selectTemplate(template)
      break
    case 'preview':
      previewTemplate(template)
      break
    case 'edit':
      editTemplate(template)
      break
    case 'copy':
      copyTemplate(template)
      break
    case 'delete':
      deleteTemplate(template)
      break
  }
}

// 编辑模板
const editTemplate = (template) => {
  ElMessage.info('编辑模板功能开发中...')
  // TODO: 打开编辑对话框
}

// 复制模板
const copyTemplate = async (template) => {
  try {
    const newTemplate = {
      ...template,
      name: `${template.name} - 副本`,
      id: undefined,
      createTime: undefined,
      updateTime: undefined
    }

    const res = await addTemplate(newTemplate)
    if (res.code === 200) {
      ElMessage.success('模板复制成功')
      getTemplates()
    } else {
      ElMessage.error(res.msg || '模板复制失败')
    }
  } catch (error) {
    console.error('复制模板失败:', error)
    ElMessage.error('复制模板失败')
  }
}

// 删除模板
const deleteTemplate = async (template) => {
  try {
    await ElMessageBox.confirm(
        `确定要删除模板"${template.name}"吗？`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    const res = await delTemplate(template.id)
    if (res.code === 200) {
      ElMessage.success('模板删除成功')
      getTemplates()
    } else {
      ElMessage.error(res.msg || '模板删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除模板失败:', error)
      ElMessage.error('删除模板失败')
    }
  }
}

// 添加新模板
const addNewTemplate = () => {
  ElMessage.info('请使用诊断报告页面的"存为模板"功能创建模板')
}

// 处理添加模板
const handleAddTemplate = () => {
  // 触发添加模板事件，让父组件处理
  emit('add-template')
}

// 截断文本
const truncateText = (text, maxLength) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// 获取检查类型标签类型
const getModalityTagType = (modality) => {
  const typeMap = {
    'CT': 'primary',
    'MRI': 'success',
    'DR': 'warning',
    'US': 'info',
    'CR': 'primary',
    'DX': 'warning'
  }
  return typeMap[modality] || 'default'
}

// 获取分类图标
const getCategoryIcon = (category) => {
  const iconMap = {
    'public': OfficeBuilding,
    'private': User
  }
  return iconMap[category] || FolderOpened
}

// 监听props变化，自动获取智能推荐
watch([() => props.modality, () => props.bodyPart], () => {
  if (props.modality || props.bodyPart) {
    getSmartRecommendations()
  }
}, {immediate: false})

// 组件挂载时获取模板
onMounted(() => {
  getTemplates()

  // 如果有检查类型或部位信息，自动获取智能推荐
  if (props.modality || props.bodyPart) {
    setTimeout(() => {
      getSmartRecommendations()
    }, 1000)
  }
})
</script>

<style scoped>
.template-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafbfc;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.title-icon {
  color: #409eff;
  font-size: 16px;
}

.search-container {
  padding: 6px 12px;
  border-bottom: 1px solid #e4e7ed;
  background: white;
}

.quick-filters {
  margin-top: 8px;
}

.quick-filters .el-button-group {
  width: 100%;
}

.quick-filters .el-button {
  flex: 1;
  font-size: 12px;
}

.template-tabs {
  border-bottom: 1px solid #e4e7ed;
}

.template-tabs :deep(.el-tabs__header) {
  margin: 0;
  padding: 0 4px;
}

.template-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0;
}

.template-tabs :deep(.el-tabs__item) {
  padding: 0 12px;
  height: 40px;
  line-height: 40px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

/* 模板树形容器样式 */
.template-tree-container {
  flex: 1;
  overflow-y: auto;
  padding: 4px;
}

/* 智能推荐样式 */
.smart-recommendations {
  margin-bottom: 8px;
  background: white;
  border-radius: 6px;
  padding: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #409eff;
}

.section-icon {
  font-size: 16px;
}

.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}


/* Tab样式 */
.template-tabs {
  height: 100%;
}

.template-tabs :deep(.el-tabs__header) {
  margin: 0 0 8px 0;
}

.template-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0 8px;
}

.template-tabs :deep(.el-tabs__content) {
  height: calc(100% - 40px);
  overflow: hidden;
}

.template-tabs :deep(.el-tab-pane) {
  height: 100%;
  overflow-y: auto;
}

/* 模板列表样式 */
.template-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 4px;
  height: 100%;
  overflow-y: auto;
}

/* 模板卡片样式 - 简洁版 */
.template-card {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.template-card:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.template-card .template-content {
  flex: 1;
  min-width: 0;
}

.template-card .template-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.template-card .template-name {
  font-weight: 600;
  font-size: 14px;
  color: #262626;
  line-height: 1.4;
  margin-bottom: 4px;
}

.template-card .template-tags {
  display: flex;
  gap: 6px;
  flex-shrink: 0;

  .el-tag {
    font-size: 11px;
    height: 20px;
    line-height: 18px;
    padding: 0 6px;
    border-radius: 4px;
  }
}

.template-card .template-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.template-card .meta-item {
  white-space: nowrap;
}

.template-card .template-preview {
  font-size: 12px;
  line-height: 1.4;
  color: #595959;
}

.template-card .preview-section {
  margin-bottom: 2px;
  display: flex;
  gap: 4px;
}

.template-card .preview-label {
  color: #909399;
  flex-shrink: 0;
}

.template-card .preview-text {
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.template-card .template-actions {
  display: flex;
  align-items: center;
  margin-left: 12px;
  opacity: 0.5;
  transition: all 0.2s ease;
}

.template-card:hover .template-actions {
  opacity: 1;
}

.template-card .action-icon {
  font-size: 16px;
  color: #909399;
  cursor: pointer;
  transition: color 0.2s ease;
}

.template-card .action-icon:hover {
  color: #409eff;
}

.apply-icon {
  font-size: 16px;
  color: #bfbfbf;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 4px;
  border-radius: 4px;
}

.apply-icon:hover {
  color: #1890ff;
  background: #f0f9ff;
  transform: scale(1.05);
}

/* 智能推荐卡片 - 极简样式 */
.smart-card {
  background: #fafbfc;
  border: 1px solid #e8f4fd;
}

.smart-card:hover {
  background: #f0f9ff;
  border-color: #bae7ff;
}

.smart-card .template-name {
  color: #1890ff;
  font-weight: 600;
}

.smart-card .template-meta {
  color: #666666;
  display: flex;
  gap: 16px;
  margin: 8px 0;

  .meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;

    .el-icon {
      font-size: 14px;
      color: #999999;
    }
  }
}

.smart-card .template-preview {
  margin-top: 12px;

  .preview-content {
    color: #666666;
    font-size: 13px;
    line-height: 1.4;
    background: #f0f9ff;
    padding: 8px 12px;
    border-radius: 4px;
  }
}

.smart-card .apply-icon {
  color: #1890ff;
  font-size: 16px;

  &:hover {
    color: #0050b3;
    background: #e6f7ff;
  }
}

.smart-card .template-actions {
  opacity: 0.7;
}

.smart-card:hover .template-actions {
  opacity: 1;
}

/* 推荐模板的标签样式调整 */
.smart-card .template-tags {
  .el-tag {
    background: #f0f9ff;
    color: #1890ff;
    border: 1px solid #bae7ff;

    &.el-tag--success {
      background: #f6ffed;
      color: #52c41a;
      border-color: #b7eb8f;
    }

    &.el-tag--warning {
      background: #fff7e6;
      color: #fa8c16;
      border-color: #ffd591;
    }
  }
}

/* 空推荐状态 */
.empty-recommend {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 组件特定样式 - 移除重复的按钮修复 */

.match-score {
  font-weight: 600;
  color: #ffd700;
}

/* 树形结构样式 */
.template-tree {
  background: white;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.template-tree-view {
  background: transparent;
}

.template-tree-view :deep(.el-tree-node__content) {
  height: auto;
  padding: 4px 0;
}

.template-tree-view :deep(.el-tree-node__expand-icon) {
  color: #409eff;
}

/* 分类节点样式 */
.tree-category-node {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  font-weight: 600;
  color: #303133;
  background: #f0f9ff;
  border-radius: 6px;
  margin: 2px 0;
}

.category-icon {
  color: #409eff;
  font-size: 16px;
}

.category-label {
  flex: 1;
  font-size: 14px;
}

.category-badge {
  margin-left: auto;
}

/* 检查类型节点样式 */
.tree-modality-node {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  margin: 2px 0;
}

.modality-count {
  color: #909399;
  font-size: 12px;
}

/* 模板节点样式 */
.tree-template-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin: 4px 0;
  background: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tree-template-node:hover {
  background: #f0f9ff;
  border-color: #409eff;
  transform: translateX(4px);
}

.template-info {
  flex: 1;
  min-width: 0;
}

.template-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.template-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-badges {
  display: flex;
  gap: 4px;
  margin-left: 8px;
}

.template-details {
  display: flex;
  gap: 12px;
  margin-bottom: 6px;
}

.detail-item {
  font-size: 12px;
  color: #909399;
}

.template-preview {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.preview-text {
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-actions {
  display: flex;
  gap: 4px;
  margin-left: 12px;
}

.template-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.template-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.template-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.template-item.personal {
  border-left: 3px solid #67c23a;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.template-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #909399;
}

.template-preview {
  margin-bottom: 12px;
}

.preview-section {
  margin-bottom: 6px;
}

.preview-label {
  font-size: 11px;
  color: #909399;
  margin-bottom: 2px;
}

.preview-content {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.template-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.apply-btn,
.preview-btn,
.edit-btn {
  padding: 4px 8px;
  font-size: 11px;
}

.add-template-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  border: 2px dashed #c0c4cc;
  border-radius: 8px;
  cursor: pointer;
  color: #909399;
  transition: all 0.3s ease;
}

.add-template-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.add-icon {
  font-size: 18px;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

/* 预览对话框样式 */
.template-preview-dialog {
  max-height: 60vh;
  overflow-y: auto;
}

.preview-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.preview-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.preview-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.preview-part {
  font-size: 14px;
  color: #606266;
}

.preview-content-full {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-section-full h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.preview-text {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .panel-header {
    padding: 12px 16px;
  }

  .search-container {
    padding: 8px 12px;
  }

  .template-container {
    padding: 12px;
  }

  .template-item {
    padding: 10px;
  }

  .template-name {
    font-size: 13px;
  }

  .template-actions {
    flex-direction: column;
    gap: 4px;
  }

  .apply-btn,
  .preview-btn,
  .edit-btn {
    width: 100%;
  }
}
</style>
