<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" tab-position="right" class="vertical-tabs">
      <el-tab-pane name="检查列表">
        <template #label>
          <div class="vertical-label">检查列表</div>
        </template>
        <el-card class="box-card">
          <!-- 搜索区域 -->
          <el-form :model="queryParams" ref="queryForm" v-show="showSearch" label-width="80px" class="search-form">
            <el-row :gutter="16">
              <el-col :xl="4" :lg="4" :md="4" :sm="11" :xs="24">
                <el-form-item label="患者ID" prop="patientId">
                  <el-input
                      v-model="queryParams.patientId"
                      placeholder="请输入患者ID"
                      clearable
                      @keyup.enter="handleQuery"
                  />
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="4" :md="4" :sm="11" :xs="24">
                <el-form-item label="患者姓名" prop="patientName">
                  <el-input
                      v-model="queryParams.patientName"
                      placeholder="请输入患者姓名"
                      clearable
                      @keyup.enter="handleQuery"
                  />
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="4" :md="4" :sm="11" :xs="24">
                <el-form-item label="检查编号" prop="studyId">
                  <el-input
                      v-model="queryParams.examCode"
                      placeholder="请输入检查编号"
                      clearable
                      @keyup.enter="handleQuery"
                  />
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="4" :md="4" :sm="11" :xs="24">
                <el-form-item label="检查项目" prop="studyDescription">
                  <el-input
                      v-model="queryParams.modality"
                      placeholder="请输入检查类型"
                      clearable
                      @keyup.enter="handleQuery"
                  />
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="4" :md="4" :sm="11" :xs="24">
                <el-form-item label="医院名称" prop="hospitalName">
                  <el-input
                      v-model="queryParams.hospitalName"
                      placeholder="请输入医院名称"
                      clearable
                      @keyup.enter="handleQuery"
                  />
                </el-form-item>
              </el-col>

              <el-col :xl="5" :lg="5" :md="5" :sm="24" :xs="24">
                <el-form-item class="search-buttons">
                  <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>

          <!-- 状态统计卡片 -->
          <div class="status-cards-container">
            <div class="status-cards">
              <div 
                class="status-card" 
                :class="{ active: statusActiveTab === 'pending' }"
                @click="handleStatusChange('pending')"
              >
                <div class="card-icon pending">
                  <el-icon><Clock /></el-icon>
                </div>
                <div class="card-content">
                  <div class="card-title">待诊断</div>
                  <div class="card-count">{{ statusCounts?.pending || 0 }}</div>
                </div>
              </div>
              
              <div 
                class="status-card" 
                :class="{ active: statusActiveTab === 'diagnosed' }"
                @click="handleStatusChange('diagnosed')"
              >
                <div class="card-icon diagnosed">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="card-content">
                  <div class="card-title">已诊断</div>
                  <div class="card-count">{{ statusCounts?.diagnosed || 0 }}</div>
                </div>
              </div>
              
              <div 
                class="status-card" 
                :class="{ active: statusActiveTab === 'audited' }"
                @click="handleStatusChange('audited')"
              >
                <div class="card-icon audited">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="card-content">
                  <div class="card-title">已审核</div>
                  <div class="card-count">{{ statusCounts?.audited || 0 }}</div>
                </div>
              </div>
              
              <div 
                class="status-card" 
                :class="{ active: statusActiveTab === 'archived' }"
                @click="handleStatusChange('archived')"
              >
                <div class="card-icon archived">
                  <el-icon><FolderOpened /></el-icon>
                </div>
                <div class="card-content">
                  <div class="card-title">院内诊断</div>
                  <div class="card-count">{{ statusCounts?.archived || 0 }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 状态列表内容 -->
          <div class="status-content">
            <div class="status-list-container">
              <!-- 统一表格 - 医疗主题 -->
              <MedicalTable
                  v-loading="loading"
                  :data="studyList"
                  @row-click="handleRowClick"
                  :row-class-name="tableRowClassName"
                  stripe
              >
                <!-- 主要信息列 - 占据大部分宽度 -->
                <el-table-column label="检查记录信息" min-width="600" align="left">
                  <template #default="scope">
                    <div class="record-info-container">
                      <!-- 第一行：主要信息 -->
                      <div class="record-main-row">
                        <div class="patient-section">
                          <el-avatar :size="32" class="patient-avatar">
                            <el-icon><User /></el-icon>
                          </el-avatar>
                          <div class="patient-basic">
                            <span class="patient-name">{{ scope.row.patientName }}</span>
                            <span class="patient-meta">{{ scope.row.patientSex }} {{ getPatientAge(scope.row) }}岁</span>
                          </div>
                        </div>

                        <div class="exam-section">
                          <div class="exam-code">{{ scope.row.examCode }}</div>
                          <el-tag size="small" :type="getModalityType(scope.row.modality)" class="modality-tag">
                            {{ scope.row.modality }}
                          </el-tag>
                        </div>

                        <div class="exam-item-section">
                          <span class="exam-item">{{ scope.row.examItem || '未指定检查项目' }}</span>
                        </div>

                        <div class="time-section">
                          <span class="exam-time">{{ formatExamTime(scope.row.checkFinishTime) }}</span>
                        </div>
                      </div>

                      <!-- 第二行：次要信息 -->
                      <div class="record-sub-row">
                        <div class="patient-detail-section">
                          <span class="patient-id">ID: {{ scope.row.originalPatientId }}</span>
                          <span class="patient-phone" v-if="scope.row.mobile">
                            <el-icon class="phone-icon"><Phone /></el-icon>
                            {{ formatPhone(scope.row.mobile) }}
                          </span>
                        </div>

                        <div class="hospital-section">
                          <span class="hospital-name">{{ scope.row.hospitalName }}</span>
                        </div>

                        <div class="diagnosis-time-section" v-if="showDiagnosisTime || showAuditTime">
                          <span v-if="showDiagnosisTime" class="diagnosis-time">
                            诊断: {{ scope.row.diagnosis?.updateTime ? formatTime(scope.row.diagnosis.updateTime) : '未诊断' }}
                          </span>
                          <span v-if="showAuditTime" class="audit-time">
                            审核: {{ scope.row.diagnosis?.auditTime ? formatTime(scope.row.diagnosis.auditTime) : '未审核' }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>

                <!-- 状态列 - 紧凑显示 -->
                <el-table-column label="状态" width="140" align="center">
                  <template #default="scope">
                    <div class="status-column">
                      <div class="status-item">
                        <el-tag
                          :type="getSyncStatusType(scope.row)"
                          :class="getSyncStatusClass(scope.row)"
                          size="small"
                          effect="light"
                        >
                          {{ getSyncStatusText(scope.row) }}
                        </el-tag>
                        <el-tooltip
                          v-if="scope.row.syncErrorMessage"
                          :content="scope.row.syncErrorMessage"
                          placement="top"
                          effect="dark"
                        >
                          <el-icon class="error-icon" color="#f56c6c">
                            <Warning />
                          </el-icon>
                        </el-tooltip>
                      </div>
                      <div class="status-item">
                        <el-tag
                          :type="getDiagnosisStatusType(scope.row)"
                          :class="getDiagnosisStatusClass(scope.row)"
                          size="small"
                        >
                          {{ getDiagnosisStatusLabel(scope.row) }}
                        </el-tag>
                      </div>
                    </div>
                  </template>
                </el-table-column>

                <!-- 操作列 -->
                <el-table-column label="操作" align="center" width="100" fixed="right">
                  <template #default="scope">
                    <el-button
                      type="primary"
                      class="medical-button"
                      size="small"
                      @click.stop="handleDiagnosis(scope.row)"
                      v-hasPermi="['diagnosis:diagnosis:edit']"
                    >
                      {{ getDiagnosisButtonLabel(scope.row) }}
                    </el-button>
                  </template>
                </el-table-column>
              </MedicalTable>
              <!-- 分页区域 - 医疗主题 -->
              <pagination
                  v-show="total > 0"
                  :total="total"
                  v-model:page="queryParams.pageNum"
                  v-model:limit="queryParams.pageSize"
                  @pagination="getList"
                  class="medical-pagination"
              />
            </div>
          </div>
        </el-card>
      </el-tab-pane>
      <el-tab-pane name="诊断">
        <template #label>
          <div class="vertical-label">诊断</div>
        </template>
        <diagnose-editor ref="diagnoseEditorRef"  @switchToStudyList="handleSwitchToStudyList"></diagnose-editor>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup name="StudyList">
import {listWithDiagnose, syncDicomData, getDiagnosisStatusCount} from "@/api/pacs/study";
import {parseTime} from '@/utils/ruoyi';
import {getCurrentInstance, onBeforeUnmount, onMounted, reactive, ref} from 'vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import {useRouter} from 'vue-router';
import DiagnoseEditor from "@/views/diagnosis/diagnose-editor.vue";
import {Clock, Document, CircleCheck, FolderOpened, User, Phone, Warning} from '@element-plus/icons-vue';
import { getDiagnosisStatusStyle, getDicomSyncStatusStyle } from '@/utils/medical-theme';

const {proxy} = getCurrentInstance();
const {diagnosis_status, diagnosis_type} = proxy.useDict('diagnosis_status', 'diagnosis_type');

const router = useRouter();

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 检查记录表格数据
const studyList = ref([]);

const diagnoseEditorRef = ref(null);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  patientId: undefined,
  patientName: undefined,
  studyId: undefined,
  studyDescription: undefined,
  diagnosisStatus: '-1',
  hospitalId: undefined,
  hospitalName: undefined
});

// 状态分栏相关数据
const statusActiveTab = ref('pending');

// 各状态的数量统计（用于卡片显示）
const statusCounts = reactive({
  pending: 0,
  diagnosed: 0,
  audited: 0,
  archived: 0
});

// 状态值映射
const statusValueMap = {
  pending: '-1',    // 待诊断
  diagnosed: '1',   // 已诊断
  audited: '2',     // 已审核
  archived: '9'     // 院内诊断
};

// 控制列显示
const showDiagnosisTime = ref(false);
const showAuditTime = ref(false);

/** 查询检查记录列表并加载诊断状态 */
const getList = async () => {
  loading.value = true;
  try {
    const res = await listWithDiagnose(queryParams);
    if (res.code === 200) {
      studyList.value = res.rows;
      total.value = res.total;
    } else {
      ElMessage.error(res.msg || '获取检查记录列表失败');
    }
  } catch (error) {
    console.error('获取检查记录列表出错', error);
    ElMessage.error('系统错误，请联系管理员');
  } finally {
    loading.value = false;
  }
};

/** 处理状态卡片点击 */
const handleStatusChange = (status) => {
  statusActiveTab.value = status;

  // 设置查询条件
  queryParams.diagnosisStatus = statusValueMap[status];
  queryParams.pageNum = 1; // 重置页码

  // 根据状态设置列显示
  updateColumnVisibility(status);

  // 发起查询
  getList();
};

/** 更新列显示状态 */
const updateColumnVisibility = (status) => {
  switch (status) {
    case 'pending':
      showDiagnosisTime.value = false;
      showAuditTime.value = false;
      break;
    case 'diagnosed':
      showDiagnosisTime.value = true;
      showAuditTime.value = false;
      break;
    case 'audited':
    case 'archived':
      showDiagnosisTime.value = true;
      showAuditTime.value = true;
      break;
    default:
      showDiagnosisTime.value = false;
      showAuditTime.value = false;
  }
};

/** 获取所有状态的统计数据 */
const getAllStatusCounts = async () => {
  try {
    const params = {
      patientId: queryParams.patientId,
      patientName: queryParams.patientName,
      examCode: queryParams.studyId,
      examItem: queryParams.studyDescription,
      hospitalId: queryParams.hospitalId,
      hospitalName: queryParams.hospitalName
    };

    const res = await getDiagnosisStatusCount(params);
    if (res.code === 200) {
      const counts = res.data;
      statusCounts.pending = counts.pending || 0;
      statusCounts.diagnosed = counts.diagnosed || 0;
      statusCounts.audited = counts.audited || 0;
      statusCounts.archived = counts.archived || 0;
    }
  } catch (error) {
    console.error('获取状态统计失败', error);
    // 重置为0
    statusCounts.pending = 0;
    statusCounts.diagnosed = 0;
    statusCounts.audited = 0;
    statusCounts.archived = 0;
  }
};

/** 重置按钮操作 */
const resetQuery = () => {
  Object.keys(queryParams).forEach(key => {
    if (key !== 'pageNum' && key !== 'pageSize') {
      queryParams[key] = undefined;
    }
  });
  queryParams.pageNum = 1;
  queryParams.diagnosisStatus = statusValueMap[statusActiveTab.value]; // 保持当前状态
  handleQuery();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1; // 重置页码
  getList(); // 获取当前状态的数据
  getAllStatusCounts(); // 更新所有状态的统计
};

/** 获取诊断状态类型 */
const getDiagnosisStatusType = (row) => {
  let status = row.diagnosis?.status || '-1';
  switch (status) {
    case '-1':
      return 'info';    // 未诊断
    case '0':
      return 'warning'; // 待诊断
    case '1':
      return 'primary'; // 已诊断
    case '2':
      return 'success'; // 已审核
    case '9':
      return 'success'; // 医院内诊断
    default:
      return 'info';    // 未知状态
  }
};

/** 获取诊断状态文本 */
const getDiagnosisStatusLabel = (row) => {
  let status = row.diagnosis?.status || '-1';
  switch (status) {
    case '-1':
      return '未诊断';
    case '0':
      return '待诊断';
    case '1':
      return '已诊断';
    case '2':
      return '已审核';
    case '9':
      return '医院内诊断';
    default:
      return '未诊断';
  }
};

/** 获取诊断状态医疗主题CSS类 */
const getDiagnosisStatusClass = (row) => {
  let status = row.diagnosis?.status || '-1';
  const statusStyle = getDiagnosisStatusStyle(status);
  return statusStyle.class;
};

/** 获取诊断按钮文本 */
const getDiagnosisButtonLabel = (row) => {
  let status = row.diagnosis?.status || '-1';
  switch (status) {
    case '-1':
      return '诊断';
    case '0':
      return '诊断';      // 待诊断
    case '1':
      return '编辑诊断';  // 已诊断
    case '2':
      return '查看诊断';  // 已审核
    case '9':
      return '查看诊断';  // 医院内诊断
    default:
      return '诊断';      // 未诊断
  }
};

/** 获取患者头像 */
const getPatientAvatar = (row) => {
  // 这里可以根据患者性别或其他信息返回不同的默认头像
  return null; // 使用默认图标
};

/** 计算患者年龄 */
const getPatientAge = (row) => {
  if (row.age) {
    return row.age;
  }
  if (row.patientBirthday) {
    const today = new Date();
    const birthDate = new Date(row.patientBirthday);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  }
  return '未知';
};

/** 格式化手机号 */
const formatPhone = (phone) => {
  if (!phone) return '';
  // 格式化为 138****1234 的形式
  if (phone.length === 11) {
    return phone.substring(0, 3) + '****' + phone.substring(7);
  }
  return phone;
};

/** 获取检查方式类型 */
const getModalityType = (modality) => {
  const modalityTypes = {
    'CT': 'primary',
    'MR': 'success',
    'MG': 'warning',
    'DX': 'info',
    'CR': 'info'
  };
  return modalityTypes[modality] || 'info';
};

/** 格式化检查时间 */
const formatExamTime = (time) => {
  if (!time) return '未完成';
  const date = new Date(time);
  const now = new Date();
  const diffTime = now - date;
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays === 1) {
    return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays < 7) {
    return diffDays + '天前';
  } else {
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
  }
};

/** 格式化时间（简短格式） */
const formatTime = (time) => {
  if (!time) return '';
  const date = new Date(time);
  return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }) + ' ' +
         date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
};

// 当前激活的标签页
const activeTab = ref('检查列表');

// 当前选中的检查记录
const currentStudy = ref(null);

/** 进入诊断页面 */
const handleDiagnosis = (row) => {
  // 设置当前选中的检查记录
  currentStudy.value = row;

  // 切换到诊断标签页
  activeTab.value = '诊断';

  // 将当前检查数据传递给诊断编辑器组件
  if (diagnoseEditorRef.value) {
    diagnoseEditorRef.value.handleStudyData(row);
  }
};

/** 表格行点击事件 */
const handleRowClick = (row) => {
 // handleDiagnosis(row);
};

/** 表格行的类名 */
const tableRowClassName = ({row}) => {
  // 如果当前行是选中的行，返回选中样式
  if (currentStudy.value && row.id === currentStudy.value.id) {
    return 'selected-row';
  }
  return '';
};

// 监听诊断编辑器发出的切换标签页事件
const handleSwitchToStudyList = () => {
  // 切换到检查列表标签页
  activeTab.value = '检查列表';
  // 刷新当前状态数据和统计
  getList();
  getAllStatusCounts();
};

/** 获取同步状态类型 */
function getSyncStatusType(row) {
  if (row.dicomSyncFlag === '1') {
    return 'success'; // 完全成功
  } else if (row.dicomSyncFlag === '2') {
    return 'warning'; // 部分成功
  } else if (row.dicomSyncFlag === '0') {
    return 'danger'; // 失败
  } else {
    return 'info'; // 待同步
  }
}

/** 获取同步状态图标 */
function getSyncStatusIcon(row) {
  if (row.dicomSyncFlag === '1') {
    return 'CircleCheck';
  } else if (row.dicomSyncFlag === '2') {
    return 'Warning';
  } else if (row.dicomSyncFlag === '0') {
    return 'CircleClose';
  } else {
    return 'Clock';
  }
}

/** 获取同步状态文本 */
function getSyncStatusText(row) {
  if (row.dicomSyncFlag === '1') {
    return '已同步';
  } else if (row.dicomSyncFlag === '2') {
    return '部分成功';
  } else if (row.dicomSyncFlag === '0') {
    return '同步失败';
  } else {
    return '待同步';
  }
}

/** 获取同步状态医疗主题CSS类 */
function getSyncStatusClass(row) {
  const status = row.dicomSyncFlag === '1' ? '1' : '0';
  const statusStyle = getDicomSyncStatusStyle(status);
  return statusStyle.class;
}

onMounted(() => {
  // 默认选中检查列表标签页
  activeTab.value = '检查列表';
  // 初始化为待诊断状态
  handleStatusChange('pending');
  getAllStatusCounts();
});

</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
}

.mb8 {
  margin-bottom: 8px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* 选中行的样式 */
:deep(.selected-row) {
  background-color: #e6f7ff !important;
  font-weight: bold;
  color: #1890ff;
}

/* 竖向标签样式 */
.vertical-tabs :deep(.el-tabs__item) {
  height: auto;
  padding: 15px 10px;
}

.vertical-label {
  writing-mode: vertical-lr;
  text-orientation: upright;
  white-space: nowrap;
  line-height: 1.2;
  letter-spacing: 2px;
  font-weight: normal;
}

/* 搜索表单样式 */
.search-form {
  background: #fafafa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 16px;
}

.search-form :deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
}

.search-buttons {
  margin-bottom: 0 !important;
}

.search-buttons :deep(.el-form-item__content) {
  display: flex;
  gap: 8px;
  justify-content: flex-start;
}

.search-buttons :deep(.el-form-item__label) {
  display: none;
}

@media (max-width: 768px) {
  .search-buttons :deep(.el-form-item__content) {
    justify-content: center;
  }
}

.search-form :deep(.el-input__wrapper) {
  border-radius: 6px;
}

/* 状态统计卡片样式 */
.status-cards-container {
  margin-top: 16px;
  margin-bottom: 16px;
}

.status-cards {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.status-card {
  flex: 1;
  min-width: 160px;
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: none;
}

.status-card:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.status-card.active {
  border-color: #1890ff;
  background: #fafbff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.12);
}

.card-icon {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.card-icon.pending {
  background: #fa8c16;
}

.card-icon.diagnosed {
  background: #1890ff;
}

.card-icon.audited {
  background: #52c41a;
}

.card-icon.archived {
  background: #722ed1;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  font-weight: 500;
  color: #595959;
  margin-bottom: 4px;
  line-height: 1.2;
}

.card-count {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  line-height: 1;
}

.status-content {
  margin-top: 20px;
}

.status-list-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 医疗主题表格样式已在全局样式中定义，这里只保留页面特定的样式 */

/* 分页样式 */
:deep(.el-pagination) {
  margin-top: 20px;
  text-align: center;
}

/* 两行布局记录信息容器 */
.record-info-container {
  padding: 12px 0;
  line-height: 1.4;
}

/* 第一行：主要信息 */
.record-main-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
  min-height: 36px;
}

/* 患者基本信息区域 */
.patient-section {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 140px;
  flex-shrink: 0;
}

.patient-avatar {
  border: 2px solid #f0f8ff;
  box-shadow: 0 2px 4px rgba(0, 102, 204, 0.1);
}

.patient-basic {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.patient-name {
  font-size: 15px;
  font-weight: 600;
  color: #262626;
  line-height: 1.2;
}

.patient-meta {
  font-size: 12px;
  color: #8c8c8c;
}

/* 检查编号和方式区域 */
.exam-section {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  flex-shrink: 0;
}

.exam-code {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  font-family: 'Courier New', monospace;
}

.modality-tag {
  flex-shrink: 0;
}

/* 检查项目区域 */
.exam-item-section {
  flex: 1;
  min-width: 0;
  margin-right: 16px;
}

.exam-item {
  font-size: 14px;
  color: #595959;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

/* 检查时间区域 */
.time-section {
  min-width: 100px;
  flex-shrink: 0;
  text-align: right;
}

.exam-time {
  font-size: 13px;
  color: #0066cc;
  font-weight: 500;
}

/* 第二行：次要信息 */
.record-sub-row {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  color: #8c8c8c;
  min-height: 20px;
}

/* 患者详细信息区域 */
.patient-detail-section {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 140px;
  flex-shrink: 0;
  margin-left: 40px; /* 对齐头像右侧 */
}

.patient-id {
  color: #0066cc;
  font-weight: 500;
}

.patient-phone {
  display: flex;
  align-items: center;
  gap: 4px;
}

.phone-icon {
  font-size: 12px;
}

/* 医院信息区域 */
.hospital-section {
  flex: 1;
  min-width: 0;
  margin-right: 16px;
}

.hospital-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

/* 诊断时间区域 */
.diagnosis-time-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
  min-width: 100px;
  flex-shrink: 0;
  text-align: right;
  font-size: 11px;
}

.diagnosis-time,
.audit-time {
  line-height: 1.2;
}

/* 状态列样式 */
.status-column {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: center;
  padding: 8px 0;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.error-icon {
  cursor: help;
}

/* 表格行样式调整 */
:deep(.medical-table .el-table__row) {
  height: auto !important;
  min-height: 70px;
}

:deep(.medical-table .el-table__row td) {
  padding: 0 !important;
  vertical-align: middle;
  border-bottom: 1px solid #f0f0f0;
}

/* 表格行悬停效果增强 */
:deep(.medical-table .el-table__row:hover) {
  background-color: #f8fbff !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 102, 204, 0.08);
  transition: all 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .record-main-row {
    gap: 12px;
  }

  .record-sub-row {
    gap: 12px;
  }

  .patient-section {
    min-width: 120px;
  }

  .exam-section {
    min-width: 100px;
  }
}

@media (max-width: 1200px) {
  .patient-name {
    font-size: 14px;
  }

  .exam-code {
    font-size: 13px;
  }

  .exam-item {
    font-size: 13px;
  }

  .record-main-row {
    gap: 8px;
  }

  .record-sub-row {
    gap: 8px;
  }
}
</style>
