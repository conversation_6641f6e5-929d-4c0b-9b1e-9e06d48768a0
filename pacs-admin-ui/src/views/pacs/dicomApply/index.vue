<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" v-show="showSearch" label-width="100px" class="search-form">
      <!-- 第一行：常用查询条件（始终显示） -->
      <el-row :gutter="4">
        <el-col :span="4">
          <el-form-item label="申请人" prop="patientName">
            <el-input
                v-model="queryParams.patientName"
                placeholder="请输入申请人"
                clearable
                @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="检查号" prop="examCode">
            <el-input
                v-model="queryParams.examCode"
                placeholder="请输入检查号"
                clearable
                @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="申请人证件号" prop="idNo">
            <el-input
                v-model="queryParams.idNo"
                placeholder="请输入申请人证件号"
                clearable
                @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="1">
          <el-form-item>
            <el-button
              type="text"
              :icon="expandForm ? 'ArrowUp' : 'ArrowDown'"
              @click="toggleExpandForm"
              class="expand-btn"
            >
              {{ expandForm ? '收起' : '展开' }}
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 展开的查询条件 -->
      <div class="expand-form" v-show="expandForm">
        <el-row :gutter="4">

          <el-col :span="4">
            <el-form-item label="申请电话" prop="phone">
              <el-input
                  v-model="queryParams.phone"
                  placeholder="请输入申请电话"
                  clearable
                  @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="患者ID" prop="originPatientId">
              <el-input
                  v-model="queryParams.originPatientId"
                  placeholder="请输入患者ID"
                  clearable
                  @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="医院名称" prop="hospitalName">
              <el-input
                  v-model="queryParams.hospitalName"
                  placeholder="请输入医院名称"
                  clearable
                  @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['pacs:dicomApply:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['pacs:dicomApply:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dicomApplyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="ID" align="center" prop="id"/>
      <el-table-column label="申请人" align="center" prop="patientName"/>
      <el-table-column label="申请人证件号" align="center" prop="idNo"/>
      <el-table-column label="申请电话" align="center" prop="phone"/>
      <el-table-column label="医院名称" align="center" prop="hospitalName" :show-overflow-tooltip="true"/>
      <el-table-column label="检查号" align="center" prop="examCode"/>
      <el-table-column label="申请状态" align="center" prop="status" width="120">
        <template #default="scope">
          <el-tag
            :type="getApplyStatusType(scope.row)"
            effect="light"
          >
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="同步状态" align="center" width="150">
        <template #default="scope">
          <div class="sync-status-container">
            <el-tag
              :type="getSyncStatusType(scope.row)"
              :icon="getSyncStatusIcon(scope.row)"
              effect="light"
            >
              {{ getSyncStatusText(scope.row) }}
            </el-tag>
            <el-tooltip
              v-if="scope.row.syncErrorMessage && scope.row.syncErrorCode !== 'SUCCESS'"
              :content="scope.row.syncErrorMessage"
              placement="top"
              effect="dark"
            >
              <el-icon class="error-icon" color="#f56c6c">
                <Warning />
              </el-icon>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="同步详情" align="center" width="220">
        <template #default="scope">
          <div v-if="scope.row.syncErrorCode && scope.row.syncErrorCode !== 'SUCCESS'">
            <div class="error-code">{{ getErrorCodeText(scope.row.syncErrorCode) }}</div>
            <div class="error-message" v-if="scope.row.syncErrorMessage">
              {{ truncateErrorMessage(scope.row.syncErrorMessage) }}
            </div>
            <div class="error-suggestion" v-if="getErrorSuggestion(scope.row.syncErrorCode)">
              <el-tag size="small" type="info" effect="plain">
                {{ getErrorSuggestion(scope.row.syncErrorCode) }}
              </el-tag>
            </div>
            <div class="sync-time" v-if="scope.row.lastSyncAttemptTime">
              最后尝试: {{ parseTime(scope.row.lastSyncAttemptTime, '{y}-{m}-{d} {h}:{i}') }}
            </div>
            <div class="retry-count" v-if="scope.row.syncRetryCount > 0">
              重试次数: {{ scope.row.syncRetryCount }}
            </div>
            <div class="study-stats" v-if="scope.row.totalStudies > 0">
              研究统计: {{ scope.row.successfulStudies || 0 }}/{{ scope.row.totalStudies }}
            </div>
            <div class="error-actions" v-if="scope.row.syncErrorMessage && scope.row.syncErrorMessage.length > 50">
              <el-button
                link
                type="primary"
                size="small"
                @click="showErrorDetail(scope.row)"
                class="detail-btn"
              >
                查看详情
              </el-button>
            </div>
          </div>
          <div v-else-if="scope.row.status === '已同步'" class="success-info">
            <div class="success-text">同步成功</div>
            <div class="study-stats" v-if="scope.row.totalStudies > 0">
              研究数量: {{ scope.row.totalStudies }}
            </div>
            <div class="sync-time" v-if="scope.row.lastSyncAttemptTime">
              {{ parseTime(scope.row.lastSyncAttemptTime, '{y}-{m}-{d} {h}:{i}') }}
            </div>
          </div>
          <div v-else class="pending-info">
            <span class="pending-text">待处理</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="患者ID" align="center" prop="originPatientId"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="180">
        <template #default="scope">
          <el-button link type="primary" @click="handleView(scope.row)">查看</el-button>
          <el-button
            link
            type="primary"
            @click="handleSync(scope.row)"
            :loading="scope.row.syncing"
          >
            {{ getSyncButtonText(scope.row) }}
          </el-button>
          <el-button
            v-if="needShowRetryButton(scope.row)"
            link
            type="warning"
            @click="handleRetrySync(scope.row)"
            :loading="scope.row.syncing"
          >
            重试
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改影像数据申请对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="dicomApplyRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="申请人" prop="patientName">
          <el-input v-model="form.patientName" placeholder="请输入申请人"/>
        </el-form-item>
        <el-form-item label="申请人证件号" prop="idNo">
          <el-input v-model="form.idNo" placeholder="请输入申请人证件号"/>
        </el-form-item>
        <el-form-item label="申请电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入申请电话"/>
        </el-form-item>
        <el-form-item label="检查号" prop="examCode">
          <el-input v-model="form.examCode" placeholder="请输入检查号"/>
        </el-form-item>
        <el-form-item label="患者ID" prop="originPatientId">
          <el-input v-model="form.originPatientId" placeholder="请输入患者ID"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog title="查看详情" v-model="viewOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="ID">{{ viewForm.id }}</el-descriptions-item>
        <el-descriptions-item label="申请人">{{ viewForm.patientName }}</el-descriptions-item>
        <el-descriptions-item label="申请人证件号">{{ viewForm.idNo }}</el-descriptions-item>
        <el-descriptions-item label="申请电话">{{ viewForm.phone }}</el-descriptions-item>
        <el-descriptions-item label="检查号">{{ viewForm.examCode }}</el-descriptions-item>
        <el-descriptions-item label="申请状态">
          <el-tag :type="getApplyStatusType(viewForm)" effect="light">
            {{ viewForm.status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="患者ID">{{ viewForm.originPatientId }}</el-descriptions-item>
        <el-descriptions-item label="同步状态">
          <el-tag :type="getSyncStatusType(viewForm)" effect="light">
            {{ getSyncStatusText(viewForm) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="同步错误代码" v-if="viewForm.syncErrorCode && viewForm.syncErrorCode !== 'SUCCESS'">
          {{ getErrorCodeText(viewForm.syncErrorCode) }}
        </el-descriptions-item>
        <el-descriptions-item label="同步错误信息" v-if="viewForm.syncErrorMessage" :span="2">
          {{ viewForm.syncErrorMessage }}
        </el-descriptions-item>
        <el-descriptions-item label="最后同步尝试" v-if="viewForm.lastSyncAttemptTime">
          {{ parseTime(viewForm.lastSyncAttemptTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="重试次数" v-if="viewForm.syncRetryCount > 0">
          {{ viewForm.syncRetryCount }}
        </el-descriptions-item>
        <el-descriptions-item label="总研究数量" v-if="viewForm.totalStudies > 0">
          {{ viewForm.totalStudies }}
        </el-descriptions-item>
        <el-descriptions-item label="成功研究数量" v-if="viewForm.successfulStudies > 0">
          {{ viewForm.successfulStudies }}
        </el-descriptions-item>
        <el-descriptions-item label="失败研究数量" v-if="viewForm.failedStudies > 0">
          {{ viewForm.failedStudies }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ viewForm.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ viewForm.updateTime }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 错误详情对话框 -->
    <el-dialog title="同步错误详情" v-model="errorDetailOpen" width="700px" append-to-body>
      <div class="error-detail-container">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="申请人">{{ errorDetailForm.patientName }}</el-descriptions-item>
          <el-descriptions-item label="检查号">{{ errorDetailForm.examCode }}</el-descriptions-item>
          <el-descriptions-item label="错误类型">
            <el-tag :type="getSyncStatusType(errorDetailForm)" effect="light">
              {{ getErrorCodeText(errorDetailForm.syncErrorCode) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="错误信息">
            <div class="error-detail-message">{{ errorDetailForm.syncErrorMessage }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="解决建议" v-if="getErrorSuggestion(errorDetailForm.syncErrorCode)">
            <div class="error-suggestion-detail">
              <el-alert
                :title="getErrorSuggestion(errorDetailForm.syncErrorCode)"
                :description="getErrorSolution(errorDetailForm.syncErrorCode)"
                type="info"
                :closable="false"
                show-icon
              />
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="最后尝试时间" v-if="errorDetailForm.lastSyncAttemptTime">
            {{ parseTime(errorDetailForm.lastSyncAttemptTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="重试次数" v-if="errorDetailForm.syncRetryCount > 0">
            {{ errorDetailForm.syncRetryCount }} 次
          </el-descriptions-item>
          <el-descriptions-item label="研究统计" v-if="errorDetailForm.totalStudies > 0">
            总数: {{ errorDetailForm.totalStudies }}，
            成功: {{ errorDetailForm.successfulStudies || 0 }}，
            失败: {{ errorDetailForm.failedStudies || 0 }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="errorDetailOpen = false">关 闭</el-button>
          <el-button
            type="primary"
            @click="handleRetryFromDetail"
            :loading="errorDetailForm.syncing"
            v-if="needShowRetryButton(errorDetailForm)"
          >
            重新同步
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DicomApply">
import {listDicomApply, getDicomApply, delDicomApply, addDicomApply, updateDicomApply} from "@/api/pacs/dicomApply";
import {syncDicomData} from "@/api/pacs/study.js";

const {proxy} = getCurrentInstance();

const dicomApplyList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const expandForm = ref(false); // 控制查询表单展开收起
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const viewOpen = ref(false);
const viewForm = ref({});
const errorDetailOpen = ref(false); // 控制错误详情弹窗
const errorDetailForm = ref({}); // 错误详情数据

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    patientName: null,
    idNo: null,
    phone: null,
    examCode: null,
    status: null,
    originPatientId: null,
    hospitalId: null,
    hospitalName: null
  },
  rules: {}
});

const {queryParams, form, rules} = toRefs(data);

/** 查询影像数据申请列表 */
function getList() {
  loading.value = true;
  listDicomApply(queryParams.value).then(response => {
    dicomApplyList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    patientName: null,
    idNo: null,
    phone: null,
    examCode: null,
    createTime: null,
    createBy: null,
    updateTime: null,
    updateBy: null,
    status: null,
    originPatientId: null
  };
  proxy.resetForm("dicomApplyRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加影像数据申请";
}


/** 手动同步按钮操作 */
function handleSync(record) {
  if (record.originPatientId == null) {
    proxy.$modal.msgError("申请记录缺少患者ID");
    return;
  }

  // 设置同步状态
  record.syncing = true;

  syncDicomData(record.originPatientId, record.examCode).then(response => {
    proxy.$modal.msgSuccess("申请已提交，正在处理中");
    getList();
  }).catch(error => {
    proxy.$modal.msgError("申请提交失败: " + (error.msg || error.message || "未知错误"));
    getList(); // 刷新列表以显示最新的错误信息
  }).finally(() => {
    record.syncing = false;
  });
}

/** 重试同步操作 */
function handleRetrySync(record) {
  proxy.$modal.confirm('确认要重新同步该申请的影像数据吗？').then(() => {
    handleSync(record);
  }).catch(() => {});
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getDicomApply(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改影像数据申请";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["dicomApplyRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateDicomApply(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addDicomApply(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除影像数据申请编号为"' + _ids + '"的数据项？').then(function () {
    return delDicomApply(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('pacs/dicomApply/export', {
    ...queryParams.value
  }, `dicomApply_${new Date().getTime()}.xlsx`)
}

/** 查看按钮操作 */
function handleView(row) {
  viewForm.value = { ...row };
  viewOpen.value = true;
}

/** 获取申请状态类型 */
function getApplyStatusType(row) {
  switch (row.status) {
    case '已同步':
      return 'success';
    case '同步失败':
      return 'danger';
    case '实时处理中':
    case '定时处理中':
      return 'warning';
    case '待同步':
    default:
      return 'info';
  }
}

/** 获取同步状态类型 */
function getSyncStatusType(row) {
  if (row.status === '已同步' && row.syncErrorCode === 'SUCCESS') {
    return 'success'; // 完全成功
  } else if (row.syncErrorCode === 'PARTIAL_FAILURE') {
    return 'warning'; // 部分成功
  } else if (row.status === '同步失败' || (row.syncErrorCode && row.syncErrorCode !== 'SUCCESS')) {
    return 'danger'; // 失败
  } else {
    return 'info'; // 待同步或处理中
  }
}

/** 获取同步状态图标 */
function getSyncStatusIcon(row) {
  if (row.status === '已同步' && row.syncErrorCode === 'SUCCESS') {
    return 'CircleCheck';
  } else if (row.syncErrorCode === 'PARTIAL_FAILURE') {
    return 'Warning';
  } else if (row.status === '同步失败' || (row.syncErrorCode && row.syncErrorCode !== 'SUCCESS')) {
    return 'CircleClose';
  } else if (row.status === '实时处理中' || row.status === '定时处理中') {
    return 'Loading';
  } else {
    return 'Clock';
  }
}

/** 获取同步状态文本 */
function getSyncStatusText(row) {
  if (row.status === '已同步' && row.syncErrorCode === 'SUCCESS') {
    return '同步成功';
  } else if (row.syncErrorCode === 'PARTIAL_FAILURE') {
    return '部分成功';
  } else if (row.status === '同步失败') {
    return '同步失败';
  } else if (row.status === '实时处理中') {
    return '实时处理中';
  } else if (row.status === '定时处理中') {
    return '定时处理中';
  } else {
    return '待同步';
  }
}

/** 获取错误代码文本 */
function getErrorCodeText(errorCode) {
  const errorCodeMap = {
    'SUCCESS': '成功',
    'NO_STUDIES_FOUND': '未找到研究',
    'CONNECTIVITY_ERROR': '连接错误',
    'PERMISSION_DENIED': '权限不足',
    'TIMEOUT': '超时',
    'PARTIAL_FAILURE': '部分失败',
    'SYSTEM_ERROR': '系统错误',
    'INTERRUPTED': '中断'
  };
  return errorCodeMap[errorCode] || errorCode;
}

/** 获取同步按钮文本 */
function getSyncButtonText(row) {
  if (row.syncing) {
    return '同步中...';
  } else if (row.status === '已同步') {
    return '重新同步';
  } else {
    return '同步';
  }
}

/** 判断是否需要显示重试按钮 */
function needShowRetryButton(row) {
  return row.status === '同步失败' || row.syncErrorCode === 'PARTIAL_FAILURE';
}

/** 切换查询表单展开收起状态 */
function toggleExpandForm() {
  expandForm.value = !expandForm.value;
}

/** 显示错误详情 */
function showErrorDetail(row) {
  errorDetailForm.value = { ...row };
  errorDetailOpen.value = true;
}

/** 从错误详情弹窗重试同步 */
function handleRetryFromDetail() {
  handleSync(errorDetailForm.value);
  errorDetailOpen.value = false;
}

/** 截断错误信息 */
function truncateErrorMessage(message) {
  if (!message) return '';
  return message.length > 50 ? message.substring(0, 50) + '...' : message;
}

/** 获取错误建议 */
function getErrorSuggestion(errorCode) {
  const suggestionMap = {
    'NO_STUDIES_FOUND': '检查患者ID',
    'CONNECTIVITY_ERROR': '检查网络连接',
    'PERMISSION_DENIED': '检查权限配置',
    'TIMEOUT': '稍后重试',
    'PARTIAL_FAILURE': '部分成功',
    'SYSTEM_ERROR': '联系技术支持',
    'INTERRUPTED': '重新同步'
  };
  return suggestionMap[errorCode] || '';
}

/** 获取错误解决方案 */
function getErrorSolution(errorCode) {
  const solutionMap = {
    'NO_STUDIES_FOUND': '请确认患者ID是否正确，或检查PACS系统中是否存在该患者的影像数据。',
    'CONNECTIVITY_ERROR': '请检查网络连接状态，确保PACS服务器可以正常访问。如问题持续，请联系网络管理员。',
    'PERMISSION_DENIED': '当前用户可能没有足够的权限访问PACS系统。请联系系统管理员检查用户权限配置。',
    'TIMEOUT': '同步请求超时，可能是由于网络延迟或服务器负载过高。建议稍后重试，或在网络状况较好时进行同步。',
    'PARTIAL_FAILURE': '部分影像数据同步成功，部分失败。建议检查失败的具体原因，必要时可重新同步。',
    'SYSTEM_ERROR': '系统内部错误，可能是服务器配置问题或软件故障。请联系技术支持团队进行排查。',
    'INTERRUPTED': '同步过程被中断，可能是由于网络断开或服务重启。请重新发起同步请求。'
  };
  return solutionMap[errorCode] || '请联系技术支持获取帮助。';
}

getList();
</script>

<style scoped>
.app-container {
  padding: 20px;
}

/* 查询表单样式 */
.search-form {
  background: #fff;
  padding: 18px 20px 0 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.search-form .el-form-item {
  margin-bottom: 18px;
}

.search-form .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

/* 展开收起按钮样式 */
.expand-btn {
  color: #409eff;
  font-size: 13px;
  padding: 0;
  margin-left: 8px;
}

.expand-btn:hover {
  color: #66b1ff;
}

/* 展开表单动画 */
.expand-form {
  animation: slideDown 0.3s ease-in-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 同步状态相关样式 */
.sync-status-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-icon {
  cursor: pointer;
  font-size: 16px;
}

.error-code {
  font-weight: bold;
  color: #f56c6c;
  font-size: 12px;
  margin-bottom: 4px;
}

.error-message {
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 4px;
  word-break: break-word;
  max-width: 180px;
}

.sync-time {
  color: #909399;
  font-size: 11px;
  margin-bottom: 2px;
}

.retry-count {
  color: #e6a23c;
  font-size: 11px;
  font-weight: bold;
  margin-bottom: 2px;
}

.study-stats {
  color: #409eff;
  font-size: 11px;
  font-weight: bold;
}

.success-info .success-text {
  color: #67c23a;
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 4px;
}

.pending-info .pending-text {
  color: #909399;
  font-size: 12px;
}

/* 错误建议样式 */
.error-suggestion {
  margin: 4px 0;
}

.error-suggestion .el-tag {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 2px;
}

/* 错误操作按钮 */
.error-actions {
  margin-top: 6px;
}

.detail-btn {
  font-size: 11px;
  padding: 2px 4px;
}

/* 错误详情弹窗样式 */
.error-detail-container {
  max-height: 500px;
  overflow-y: auto;
}

.error-detail-message {
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #f56c6c;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #606266;
  word-break: break-word;
  white-space: pre-wrap;
}

.error-suggestion-detail {
  margin-top: 8px;
}

.error-suggestion-detail .el-alert {
  border-radius: 4px;
}

.error-suggestion-detail .el-alert__title {
  font-size: 14px;
  font-weight: 600;
}

.error-suggestion-detail .el-alert__description {
  font-size: 13px;
  line-height: 1.6;
  margin-top: 8px;
}

/* 表格行状态样式 */
.el-table .el-table__row.apply-failed {
  background-color: #fef0f0;
}

.el-table .el-table__row.apply-processing {
  background-color: #fdf6ec;
}

.el-table .el-table__row.apply-success {
  background-color: #f0f9ff;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .error-message {
    max-width: 120px;
  }

  .sync-time {
    font-size: 10px;
  }
}
</style>
