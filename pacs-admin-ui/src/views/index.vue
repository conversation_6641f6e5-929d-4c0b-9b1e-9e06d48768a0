<template>
  <div class="app-container home">
    <el-row :gutter="20" align="middle">
      <el-col :sm="24" :lg="12" style="padding-left: 20px">
        <h2>云影像系统 - 数据统计</h2>
      </el-col>
      <el-col :sm="24" :lg="12" style="text-align: right;">
        <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
            :clearable="false"
        />
        <el-button type="primary" icon="Refresh" circle @click="refreshData" style="margin-left: 10px;"></el-button>
      </el-col>
    </el-row>

    <!-- Date Range Display -->
    <el-row v-if="dateRange && dateRange.length === 2" style="margin-top: 10px; margin-bottom: 10px;">
      <el-col :span="24" style="text-align: center; color: #666; font-size: 14px;">
        当前统计时间范围：{{ dateRange[0] }} 至 {{ dateRange[1] }}
      </el-col>
    </el-row>

    <!-- Summary Cards -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="summary-card">
          <div class="card-content">
            <div class="icon-wrapper bg-blue">
              <el-icon>
                <DataAnalysis/>
              </el-icon>
            </div>
            <div class="text-wrapper">
              <p class="value">{{ applyCount }}</p>
              <p class="label">影像申请总数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="summary-card">
          <div class="card-content">
            <div class="icon-wrapper bg-green">
              <el-icon>
                <Finished/>
              </el-icon>
            </div>
            <div class="text-wrapper">
              <p class="value">{{ applySyncedCount }}</p>
              <p class="label">影像申请已同步数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="summary-card">
          <div class="card-content">
            <div class="icon-wrapper bg-orange">
              <el-icon>
                <Clock/>
              </el-icon>
            </div>
            <div class="text-wrapper">
              <p class="value">{{ applyUnsyncedCount }}</p>
              <p class="label">影像申请待同步数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="summary-card">
          <div class="card-content">
            <div class="icon-wrapper bg-purple">
              <el-icon>
                <Document/>
              </el-icon>
            </div>
            <div class="text-wrapper">
              <p class="value">{{ syncedStudyCount }}/{{ studyCount }}</p>
              <p class="label">已同步检查数</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Charts -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- Trend Chart -->
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <el-card shadow="hover">
          <template #header>
            <span>申请趋势</span>
          </template>
          <div ref="trendChartRef" style="height: 400px;"></div>
        </el-card>
      </el-col>
      <!-- Status Pie Chart -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <el-card shadow="hover">
          <template #header>
            <span>申请状态分布</span>
          </template>
          <div ref="statusPieChartRef" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

  </div>
</template>

<script setup name="Index">
import {ref, onMounted, onBeforeUnmount} from 'vue';
// 确保已安装 echarts: npm install echarts 或 yarn add echarts
import * as echarts from 'echarts';
// TODO: 替换为实际的 API 导入和调用, 例如:
// import { getStatisticsData } from '@/api/statistics'; // 旧的假设 API 路径
// 导入您的 API 请求函数，请根据实际情况修改路径
import request from '@/utils/request';
// 导入 Element Plus 图标
import {DataAnalysis, Finished, Clock, Document, Refresh} from '@element-plus/icons-vue';

const version = ref('3.8.9'); // Existing variable

function goTarget(url) { // Existing function
  window.open(url, '__blank');
}

// --- 统计数据和图表逻辑 ---

// --- 日期范围选择 --- 
const dateRange = ref([]);

// 设置默认日期范围为当月
const setDefaultDateRange = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth();
  const firstDayOfMonth = new Date(year, month, 1);
  const lastDayOfMonth = new Date(year, month + 1, 0);

  // 格式化为 YYYY-MM-DD
  const formatDate = (date) => {
    const y = date.getFullYear();
    const m = String(date.getMonth() + 1).padStart(2, '0');
    const d = String(date.getDate()).padStart(2, '0');
    return `${y}-${m}-${d}`;
  }

  dateRange.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
};

// 处理日期变化
const handleDateChange = () => {
  // 确保选择了日期范围
  if (dateRange.value && dateRange.value.length === 2) {
    getStatistics(dateRange.value[0], dateRange.value[1]);
  } else {
    // 如果日期被清空（虽然 clearable=false，以防万一），可以获取默认数据或提示用户
    // getStatistics(); 
    console.warn("日期范围选择无效");
  }
};

// Reactive variables for statistics
const applyCount = ref(0);
const applySyncedCount = ref(0);
const applyUnsyncedCount = ref(0);
const studyCount = ref(0);
const syncedStudyCount = ref(0);
const applyTrend = ref([]);
const syncedTrend = ref([]);
const unsyncedTrend = ref([]);

// Refs for chart DOM elements
const trendChartRef = ref(null);
const statusPieChartRef = ref(null);

// Chart instances
let trendChartInstance = null;
let pieChartInstance = null;

// Fetch data function
const getStatistics = async (start = null, end = null) => {
  try {
    console.log(`正在获取统计数据 (范围: ${start} - ${end})...`); // Debug log with date range

    // --- ACTUAL API CALL ---
    const params = {};
    if (start) params.start = start;
    if (end) params.end = end;

    // 使用导入的 request 函数发起 GET 请求
    // 假设 request 函数返回的数据结构是 { code: ..., msg: ..., data: ... }
    // 并且后端实际返回的数据就在 response.data 中
    const response = await request({
      url: '/datasync/study/stat',
      method: 'get',
      params: params // 将 start 和 end 作为查询参数传递
    });

    console.log("获取到的 API 数据:", response); // Debug log for entire response

    // --- END ACTUAL API CALL ---

    // 检查 response 和 response.data 是否存在
    // !!! 重要: 请根据您的 request 函数和后端实际返回的结构调整这里的判断 !!!
    // 例如，如果成功的数据总是在 response.data 里:
    if (response && response.data) {
      const data = response.data; // 直接使用 response.data
      applyCount.value = data.applyCount || 0;
      applySyncedCount.value = data.applySyncedCount || 0;
      applyUnsyncedCount.value = data.applyUnsyncedCount || 0;
      studyCount.value = data.studyCount || 0;
      syncedStudyCount.value = data.syncedStudyCount || 0;
      // 确保趋势数据是数组
      applyTrend.value = Array.isArray(data.applyTrend) ? data.applyTrend : [];
      syncedTrend.value = Array.isArray(data.syncedTrend) ? data.syncedTrend : [];
      unsyncedTrend.value = Array.isArray(data.unsyncedTrend) ? data.unsyncedTrend : [];

      console.log("数据已更新, 准备初始化图表..."); // Debug log
      // 数据加载后初始化图表
      initCharts();
    } else {
      console.error("获取到的数据格式不正确或数据为空:", response);
      // 设置默认值或显示错误消息
      applyCount.value = 0;
      applySyncedCount.value = 0;
      applyUnsyncedCount.value = 0;
      studyCount.value = 0;
      applyTrend.value = [];
      syncedTrend.value = [];
      unsyncedTrend.value = [];
      // 即使数据为空也尝试初始化图表（显示"暂无数据"）
      initCharts();
    }

  } catch (error) {
    console.error("获取统计数据失败:", error);
    // TODO: 使用 ElMessage 或类似方式向用户显示错误
    // 即使出错也尝试初始化图表（显示"暂无数据"）
    initCharts();
  }
};

// Initialize/Update charts function
const initCharts = () => {
  console.log("初始化图表函数执行..."); // Debug log
  // 定义颜色方案
  const colorPalette = ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC'];

  // --- Trend Chart ---
  if (trendChartRef.value) {
    console.log("趋势图容器存在，开始初始化..."); // Debug log
    try {
      // Dispose existing instance before re-initializing
      if (trendChartInstance) {
        trendChartInstance.dispose();
      }
      trendChartInstance = echarts.init(trendChartRef.value);
      console.log("趋势图实例创建成功"); // Debug log

      // 检查是否有有效的趋势数据
      const hasTrendData = applyTrend.value.length > 0 || syncedTrend.value.length > 0 || unsyncedTrend.value.length > 0;
      console.log("趋势数据状态:", {
        applyTrend: applyTrend.value.length,
        syncedTrend: syncedTrend.value.length,
        unsyncedTrend: unsyncedTrend.value.length
      }); // Debug log

      const dates = hasTrendData ? (applyTrend.value.length > 0 ? applyTrend.value.map(item => item.date) : (syncedTrend.value.length > 0 ? syncedTrend.value.map(item => item.date) : unsyncedTrend.value.map(item => item.date))) : [];

      // 无论是否有数据，都设置图表选项
      const trendOption = {
        color: colorPalette,
        tooltip: {trigger: 'axis'},
        legend: {
          data: ['总申请数', '已同步', '待同步'],
          top: 10,
          itemGap: 15,
          textStyle: {color: '#666'}
        },
        grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true},
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates,
          axisLine: {lineStyle: {color: '#ccc'}},
          axisLabel: {color: '#666'}
        },
        yAxis: {
          type: 'value',
          axisLine: {show: true, lineStyle: {color: '#ccc'}},
          splitLine: {lineStyle: {type: 'dashed', color: '#eee'}},
          axisLabel: {color: '#666'}
        },
        series: [
          {
            name: '总申请数',
            type: 'line',
            smooth: true,
            data: hasTrendData ? applyTrend.value.map(item => item.count) : [],
            areaStyle: {opacity: 0.1}
          },
          {
            name: '已同步',
            type: 'line',
            smooth: true,
            data: hasTrendData ? syncedTrend.value.map(item => item.count) : [],
            areaStyle: {opacity: 0.1}
          },
          {
            name: '待同步',
            type: 'line',
            smooth: true,
            data: hasTrendData ? unsyncedTrend.value.map(item => item.count) : [],
            areaStyle: {opacity: 0.1}
          }
        ]
      };

      // 如果没有数据，添加标题提示
      if (!hasTrendData) {
        trendOption.title = {
          text: '暂无趋势数据',
          left: 'center',
          top: 'center',
          textStyle: {color: '#888', fontSize: 14}
        };
      }

      trendChartInstance.setOption(trendOption);
      console.log("趋势图选项设置完成"); // Debug log

    } catch (error) {
      console.error("趋势图初始化失败:", error); // Error log
    }
  } else {
    console.warn("趋势图容器不存在，请检查 DOM 结构"); // Warning log
  }

  // --- Status Pie Chart ---
  if (statusPieChartRef.value) {
    console.log("饼图容器存在，开始初始化..."); // Debug log
    try {
      // Dispose existing instance
      if (pieChartInstance) {
        pieChartInstance.dispose();
      }
      pieChartInstance = echarts.init(statusPieChartRef.value);
      console.log("饼图实例创建成功"); // Debug log

      // 检查是否有饼图数据
      const hasPieData = (applySyncedCount.value || 0) + (applyUnsyncedCount.value || 0) > 0;
      console.log("饼图数据状态:", {
        syncedCount: applySyncedCount.value,
        unsyncedCount: applyUnsyncedCount.value
      }); // Debug log

      // 无论是否有数据，都设置图表选项
      const pieOption = {
        color: colorPalette.slice(1, 3),
        tooltip: {trigger: 'item', formatter: '{a} <br/>{b}: {c} ({d}%)'},
        legend: {top: '5%', left: 'center'},
        series: [
          {
            name: '申请状态分布',
            type: 'pie',
            radius: ['50%', '75%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 8,
              borderColor: '#fff',
              borderWidth: 3,
            },
            label: {show: false, position: 'center'},
            emphasis: {
              label: {show: true, fontSize: '16', fontWeight: 'bold'},
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            labelLine: {show: false},
            data: hasPieData ? [
              {value: applySyncedCount.value || 0, name: '已同步'},
              {value: applyUnsyncedCount.value || 0, name: '待同步'},
            ] : []
          }
        ]
      };

      // 如果没有数据，添加标题提示
      if (!hasPieData) {
        pieOption.title = {
          text: '暂无状态数据',
          left: 'center',
          top: 'center',
          textStyle: {color: '#888', fontSize: 14}
        };
      }

      pieChartInstance.setOption(pieOption);
      console.log("饼图选项设置完成"); // Debug log

    } catch (error) {
      console.error("饼图初始化失败:", error); // Error log
    }
  } else {
    console.warn("饼图容器不存在，请检查 DOM 结构"); // Warning log
  }

  // 添加 resize 监听器
  window.addEventListener('resize', resizeCharts);
};

// Resize charts function
const resizeCharts = () => {
  if (trendChartInstance) {
    trendChartInstance.resize();
  }
  if (pieChartInstance) {
    pieChartInstance.resize();
  }
};

// Cleanup on unmount
onBeforeUnmount(() => {
  console.log("组件卸载，移除监听器并销毁图表实例"); // Debug log
  window.removeEventListener('resize', resizeCharts);
  if (trendChartInstance) {
    trendChartInstance.dispose();
    trendChartInstance = null;
  }
  if (pieChartInstance) {
    pieChartInstance.dispose();
    pieChartInstance = null;
  }
});

// Run on mount
onMounted(() => {
  console.log("组件已挂载，设置默认日期并获取数据..."); // Debug log
  setDefaultDateRange(); // 设置默认日期范围
  // 确保在设置默认值后获取数据
  if (dateRange.value && dateRange.value.length === 2) {
    getStatistics(dateRange.value[0], dateRange.value[1]);
  } else {
    console.error("未能设置默认日期范围，无法获取初始数据");
  }
});

// 手动刷新数据
const refreshData = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    getStatistics(dateRange.value[0], dateRange.value[1]);
  } else {
    console.warn("日期范围无效，无法刷新");
    // 可选：如果日期无效，尝试使用默认值刷新或提示用户
    // setDefaultDateRange();
    // getStatistics(dateRange.value[0], dateRange.value[1]); 
  }
};

</script>

<style scoped lang="scss">
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }

  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }

  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }

  // Improved card styles
  .summary-card {
    .el-card__body {
      padding: 15px; // Adjust padding
    }

    .card-content {
      display: flex;
      align-items: center; // Vertically align icon and text
      justify-content: space-between; // Push icon and text apart
    }

    .icon-wrapper {
      font-size: 28px; // Icon size
      color: #fff; // Icon color
      border-radius: 6px; // Rounded corners for background
      width: 50px; // Fixed width
      height: 50px; // Fixed height
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
    }

    .text-wrapper {
      text-align: right;

      .value {
        font-size: 24px; // Larger value font size
        font-weight: 600; // Bold
        color: #333;
        margin: 0 0 4px 0;
      }

      .label {
        font-size: 13px;
        color: #888;
        margin: 0;
      }
    }

    // Icon background colors (example)
    .bg-blue {
      background-color: #409EFF;
    }

    .bg-green {
      background-color: #67C23A;
    }

    .bg-orange {
      background-color: #E6A23C;
    }

    .bg-purple {
      background-color: #905dd1;
    }

    // Example purple
  }

  // Ensure cards have minimum height if needed
  .el-card {
    margin-bottom: 20px; // Add bottom margin to cards for spacing on smaller screens
  }

  // Ensure chart containers are ready
  [ref="trendChartRef"], [ref="statusPieChartRef"] {
    width: 100%;
    height: 400px; // Ensure height is set
  }
}
</style>
