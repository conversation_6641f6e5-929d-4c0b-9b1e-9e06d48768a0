# 前端诊断编辑器医生信息同步调整说明

## 更新概述

本次更新同步调整了前端 `diagnose-editor.vue` 组件，使其与后端 `prepareReportData` 方法的增强保持一致，支持显示报告医生和审核医生的详细信息（姓名和签名图片）。

## 主要变更

### 1. 新增数据结构

在组件中新增了两个响应式对象，与后端返回的数据结构保持一致：

```javascript
// 医生信息对象（与后端 prepareReportData 保持一致）
const reportDoctorInfo = ref({
  name: '',
  signatureUrl: ''
});

const auditDoctorInfo = ref({
  name: '',
  signatureUrl: ''
});
```

### 2. 模板更新

更新了签名区域的模板，优先使用新的医生信息对象：

```vue
<!-- 报告医生信息 -->
<div class="signature-item">
  <span class="signature-label">报告医生：</span>
  <div class="signature-content">
    <div class="signature-image-wrapper" v-if="reportDoctorInfo.signatureUrl">
      <img :src="reportDoctorInfo.signatureUrl" alt="医生签名" class="signature-image" />
    </div>
    <span class="signature-name" v-else>{{ reportDoctorInfo.name || diagnosisForm.doctor || userStore.name }}</span>
  </div>
</div>

<!-- 审核医生信息 -->
<div class="signature-item">
  <span class="signature-label">审核医生：</span>
  <div class="signature-content">
    <div class="signature-image-wrapper" v-if="auditDoctorInfo.signatureUrl && auditDoctorInfo.name">
      <img :src="auditDoctorInfo.signatureUrl" alt="审核医生签名" class="signature-image" />
    </div>
    <span class="signature-name" v-else>{{ auditDoctorInfo.name || diagnosisForm.auditBy || '' }}</span>
  </div>
</div>
```

### 3. 新增方法

#### getDoctorInfo(userName)
```javascript
/**
 * 获取医生详细信息（姓名和签名URL）
 * @param {string} userName - 医生用户名
 * @returns {Object} 包含 name 和 signatureUrl 的对象
 */
const getDoctorInfo = async (userName) => {
  const doctorInfo = {
    name: '',
    signatureUrl: ''
  };

  if (!userName) {
    return doctorInfo;
  }

  try {
    // 如果是当前用户，直接使用store中的信息
    if (userName === userStore.name) {
      doctorInfo.name = userStore.nickName || userStore.name || userName;
      doctorInfo.signatureUrl = userStore.avatar || '';
      return doctorInfo;
    }

    // 对于其他用户，调用API获取用户信息
    const res = await getUserByName(userName);
    if (res.code === 200 && res.data) {
      const user = res.data;
      // 优先使用昵称，其次使用用户名
      doctorInfo.name = user.nickName || user.userName || userName;
      
      // 处理签名图片URL
      if (user.avatar) {
        let avatar = user.avatar;
        // 如果是相对路径，转换为完整URL
        if (!avatar.startsWith('http')) {
          avatar = import.meta.env.VITE_APP_BASE_API + avatar;
        }
        doctorInfo.signatureUrl = avatar;
      }
    } else {
      // 如果找不到用户，只设置姓名
      doctorInfo.name = userName;
    }

    return doctorInfo;
  } catch (error) {
    console.error('获取医生信息失败:', error);
    // 异常情况下只设置姓名
    doctorInfo.name = userName;
    return doctorInfo;
  }
};
```

### 4. 更新现有方法

#### loadDoctorSignature()
```javascript
// 加载医生签名图片
const loadDoctorSignature = async () => {
  const doctorName = diagnosisForm.doctor || userStore.name;
  doctorSignatureUrl.value = await getUserSignature(doctorName);
  // 同时更新医生信息对象
  reportDoctorInfo.value = await getDoctorInfo(doctorName);
};
```

#### loadAuditSignature()
```javascript
// 加载审核医生签名图片
const loadAuditSignature = async () => {
  if (diagnosisForm.auditBy) {
    auditSignatureUrl.value = await getUserSignature(diagnosisForm.auditBy);
    // 同时更新审核医生信息对象
    auditDoctorInfo.value = await getDoctorInfo(diagnosisForm.auditBy);
  } else {
    auditSignatureUrl.value = '';
    auditDoctorInfo.value = { name: '', signatureUrl: '' };
  }
};
```

#### handleSignatureError()
```javascript
// 处理签名图片加载错误
const handleSignatureError = (type) => {
  console.warn(`${type === 'doctor' ? '报告医生' : '审核医生'}签名图片加载失败`);
  if (type === 'doctor') {
    doctorSignatureUrl.value = '';
    reportDoctorInfo.value.signatureUrl = '';
  } else {
    auditSignatureUrl.value = '';
    auditDoctorInfo.value.signatureUrl = '';
  }
};
```

### 5. 生命周期更新

在 `onMounted` 中添加了审核医生签名的加载：

```javascript
onMounted(async () => {
  // ... 其他初始化代码

  // 加载医生签名
  await loadDoctorSignature();
  
  // 如果有审核医生，加载审核医生签名
  if (diagnosisForm.auditBy) {
    await loadAuditSignature();
  }

  // ... 其他代码
});
```

## 向后兼容性

### 1. 保留原有字段
- `doctorSignatureUrl` - 保留原有的医生签名URL字段
- `auditSignatureUrl` - 保留原有的审核医生签名URL字段
- `getUserSignature()` - 保留原有的获取签名方法

### 2. 渐进式增强
- 新的医生信息对象作为增强功能
- 模板中优先使用新对象，但保留原有字段作为后备
- 现有功能不受影响

## 数据流程

### 1. 初始化流程
```
组件挂载 → 加载医生签名 → 更新医生信息对象 → 渲染界面
```

### 2. 审核流程
```
执行审核 → 更新审核医生字段 → 加载审核医生签名 → 更新审核医生信息对象
```

### 3. 错误处理
```
签名加载失败 → 清空签名URL → 保留医生姓名 → 显示文字签名
```

## 显示逻辑

### 1. 报告医生显示优先级
1. `reportDoctorInfo.signatureUrl` - 新的签名图片URL
2. `reportDoctorInfo.name` - 新的医生姓名
3. `diagnosisForm.doctor` - 原有的医生用户名
4. `userStore.name` - 当前用户名

### 2. 审核医生显示优先级
1. `auditDoctorInfo.signatureUrl` - 新的签名图片URL（需要同时有姓名）
2. `auditDoctorInfo.name` - 新的审核医生姓名
3. `diagnosisForm.auditBy` - 原有的审核医生用户名
4. 空字符串 - 无审核医生时

## 错误处理

### 1. 网络错误
- API调用失败时使用用户名作为显示名称
- 签名图片加载失败时显示文字签名
- 记录详细的错误日志便于调试

### 2. 数据缺失
- 用户不存在时使用原始用户名
- 头像字段为空时不显示签名图片
- 昵称为空时使用用户名

### 3. 权限问题
- API权限不足时降级为显示用户名
- 图片访问权限不足时显示默认签名

## 性能优化

### 1. 避免重复请求
- 相同用户名只请求一次用户信息
- 当前用户信息直接从store获取
- 缓存用户信息减少API调用

### 2. 异步加载
- 签名图片异步加载，不阻塞界面渲染
- 错误处理不影响其他功能
- 使用Promise.all并行加载多个签名

### 3. 内存管理
- 组件销毁时清理定时器
- 避免内存泄漏
- 合理使用响应式数据

## 测试建议

### 1. 功能测试
- 测试报告医生信息正确显示
- 测试审核医生信息正确显示
- 测试签名图片加载和显示
- 测试错误情况的降级处理

### 2. 兼容性测试
- 确保现有功能正常工作
- 测试新旧数据结构混合使用
- 验证向后兼容性

### 3. 性能测试
- 测试大量医生信息加载的性能
- 监控API调用次数和响应时间
- 验证内存使用情况

## 后续优化方向

1. **缓存机制**：添加用户信息本地缓存
2. **批量加载**：支持批量获取多个用户信息
3. **实时更新**：支持用户信息变更的实时同步
4. **离线支持**：支持离线模式下的基本功能
5. **国际化**：支持多语言显示
