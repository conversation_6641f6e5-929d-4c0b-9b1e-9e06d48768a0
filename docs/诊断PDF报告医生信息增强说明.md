# 诊断PDF报告医生信息增强说明

## 更新概述

本次更新增强了诊断PDF报告生成功能，在 `prepareReportData` 方法中添加了报告医生和审核医生的详细信息，包括姓名和签名图片的完整URL。

## 功能特性

### 1. 新增数据字段

在PDF报告数据中新增以下字段：

- **reportDoctor** - 报告医生信息对象
  - `name` - 医生姓名（优先使用昵称，其次用户名）
  - `signatureUrl` - 签名图片的完整URL
  
- **auditDoctor** - 审核医生信息对象
  - `name` - 医生姓名（优先使用昵称，其次用户名）
  - `signatureUrl` - 签名图片的完整URL

### 2. 向后兼容性

保留了原有的 `doctorSignature` 字段，确保现有模板仍能正常工作。

## 技术实现

### 1. 数据来源

- **报告医生**：从 `diagnosis.doctor` 字段获取用户名，查询 `sys_user` 表获取详细信息
- **审核医生**：从 `diagnosis.auditBy` 字段获取用户名，查询 `sys_user` 表获取详细信息

### 2. 核心方法

#### getDoctorInfo(String doctorName)
```java
/**
 * 获取医生信息（姓名和签名图片）
 * @param doctorName 医生用户名
 * @return 包含姓名和签名URL的Map对象
 */
private Map<String, Object> getDoctorInfo(String doctorName)
```

**功能说明**：
- 根据用户名查询用户详细信息
- 优先使用昵称作为显示姓名
- 获取用户头像作为签名图片
- 处理相对路径转换为完整URL
- 异常情况下使用默认值

#### getUserSignatureUrl(SysUser user)
```java
/**
 * 获取用户签名图片URL
 * @param user 用户对象
 * @return 签名图片的完整URL
 */
private String getUserSignatureUrl(SysUser user)
```

**功能说明**：
- 从用户头像字段获取签名图片
- 自动处理相对路径转换
- 支持配置文件服务器地址
- 提供默认签名机制

### 3. URL处理逻辑

#### 文件服务器配置
- 配置键：`file.server.url`
- 默认值：`http://localhost:9000`（MinIO默认地址）

#### URL转换规则
```java
// 相对路径示例：profile/avatar/2024/01/15/xxx.jpg
// 转换后：http://localhost:9000/profile/avatar/2024/01/15/xxx.jpg

if (!avatar.startsWith("http")) {
    String fileServerUrl = sysConfigService.selectConfigByKey("file.server.url");
    avatar = fileServerUrl + "/" + avatar;
}
```

## 数据结构

### 报告数据结构示例

```json
{
  "hospitalName": "鄂托克旗人民医院",
  "reportTitle": "CT检查报告",
  "diagnosis": { /* 诊断对象 */ },
  "study": { /* 检查信息对象 */ },
  "qrCode": "https://example.com/?query=12345",
  
  // 新增字段
  "reportDoctor": {
    "name": "张医生",
    "signatureUrl": "http://localhost:9000/profile/avatar/2024/01/15/doctor1.jpg"
  },
  "auditDoctor": {
    "name": "李主任", 
    "signatureUrl": "http://localhost:9000/profile/avatar/2024/01/15/doctor2.jpg"
  },
  
  // 保持向后兼容
  "doctorSignature": "http://localhost:9000/profile/avatar/current_user.jpg"
}
```

## 使用方式

### 1. 在报告模板中使用

```html
<!-- 报告医生信息 -->
<div class="doctor-info">
  <span>报告医生：{{reportDoctor.name}}</span>
  <img src="{{reportDoctor.signatureUrl}}" alt="医生签名" class="signature" />
</div>

<!-- 审核医生信息 -->
<div class="audit-info">
  <span>审核医生：{{auditDoctor.name}}</span>
  <img src="{{auditDoctor.signatureUrl}}" alt="医生签名" class="signature" />
</div>
```

### 2. 条件渲染

```html
<!-- 仅在有审核医生时显示 -->
{{#if auditDoctor.name}}
<div class="audit-section">
  <span>审核医生：{{auditDoctor.name}}</span>
  {{#if auditDoctor.signatureUrl}}
  <img src="{{auditDoctor.signatureUrl}}" alt="审核医生签名" />
  {{/if}}
</div>
{{/if}}
```

## 配置说明

### 1. 必要配置

在 `sys_config` 表中配置文件服务器地址：

```sql
INSERT INTO sys_config (config_name, config_key, config_value, config_type, remark) 
VALUES ('文件服务器地址', 'file.server.url', 'http://your-file-server:9000', 'N', '用于生成完整的文件访问URL');
```

### 2. 默认签名配置

```sql
INSERT INTO sys_config (config_name, config_key, config_value, config_type, remark) 
VALUES ('默认医生签名', 'diagnosis.pdf.defaultSignature', 'http://your-server/default-signature.png', 'N', '当医生未设置签名时使用的默认签名图片');
```

## 异常处理

### 1. 用户不存在
- 仅设置姓名为原始用户名
- 签名URL使用默认签名

### 2. 签名图片不存在
- 使用默认签名图片
- 记录警告日志

### 3. 文件服务器配置缺失
- 使用默认MinIO地址
- 记录警告日志

## 日志记录

系统会记录以下关键信息：

```java
log.info("获取到医生信息: 姓名={}, 签名URL={}", displayName, signatureUrl);
log.warn("未找到医生用户信息: {}, 使用默认签名", doctorName);
log.error("获取医生信息失败: {}", e.getMessage(), e);
```

## 性能考虑

### 1. 数据库查询优化
- 每个医生信息只查询一次
- 使用用户名索引提高查询效率

### 2. 缓存机制
- 可考虑添加用户信息缓存
- 减少重复的数据库查询

### 3. 异常处理
- 快速失败机制
- 避免因个别用户信息缺失影响整体生成

## 测试建议

### 1. 功能测试
- 测试报告医生信息正确显示
- 测试审核医生信息正确显示
- 测试用户不存在的情况
- 测试签名图片不存在的情况

### 2. 兼容性测试
- 确保现有模板仍能正常工作
- 测试新旧字段同时使用的情况

### 3. 性能测试
- 测试大量报告生成的性能影响
- 监控数据库查询次数和响应时间

## 后续优化建议

1. **缓存机制**：添加用户信息缓存，减少数据库查询
2. **批量查询**：支持批量获取多个用户信息
3. **签名管理**：独立的医生签名管理功能
4. **模板增强**：提供更多医生信息字段（职称、科室等）
5. **权限控制**：控制签名图片的访问权限
